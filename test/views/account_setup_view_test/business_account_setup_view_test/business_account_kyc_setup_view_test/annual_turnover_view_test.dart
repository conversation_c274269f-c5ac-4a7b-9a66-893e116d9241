import 'package:country_picker/country_picker.dart';
import 'package:exchek/core/enums/app_enums.dart';
import 'package:exchek/views/account_setup_view/business_account_setup_view/business_account_kyc_setup_view/annual_turnover_view.dart';
import 'package:exchek/viewmodels/account_setup_bloc/business_account_setup_bloc/business_account_setup_bloc.dart';
import 'package:exchek/widgets/common_widget/custom_textfields.dart';
import 'package:exchek/widgets/common_widget/custom_button.dart';
import 'package:exchek/widgets/common_widget/app_file_upload_widget.dart';
import 'package:exchek/widgets/account_setup_widgets/custom_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:exchek/core/generated/l10n.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:exchek/core/utils/local_storage.dart';
import 'annual_turnover_view_test.mocks.dart';

@GenerateMocks([BusinessAccountSetupBloc])
BusinessAccountSetupState createTestState({
  bool isGstCertificateMandatory = false,
  FileData? gstCertificateFile,
  bool? isGstVerificationLoading,
  KycVerificationSteps currentKycVerificationStep = KycVerificationSteps.annualTurnoverDeclaration,
  String turnoverText = '',
  String gstNumberText = '',
  String? selectedAnnualTurnover,
  bool isGSTNumberVerify = false,
}) {
  final turnOverController = TextEditingController();
  final gstNumberController = TextEditingController();
  turnOverController.text = turnoverText;
  gstNumberController.text = gstNumberText;

  return BusinessAccountSetupState(
    isGstCertificateMandatory: isGstCertificateMandatory,
    gstCertificateFile: gstCertificateFile,
    isGstVerificationLoading: isGstVerificationLoading,
    currentKycVerificationStep: currentKycVerificationStep,
    turnOverController: turnOverController,
    gstNumberController: gstNumberController,
    selectedAnnualTurnover: selectedAnnualTurnover,
    isGSTNumberVerify: isGSTNumberVerify,
    annualTurnoverFormKey: GlobalKey<FormState>(),
    // Required fields for BusinessAccountSetupState
    currentStep: BusinessAccountSetupSteps.businessInformation,
    goodsAndServiceExportDescriptionController: TextEditingController(),
    goodsExportOtherController: TextEditingController(),
    serviceExportOtherController: TextEditingController(),
    businessActivityOtherController: TextEditingController(),
    scrollController: ScrollController(),
    formKey: GlobalKey<FormState>(),
    businessLegalNameController: TextEditingController(),
    professionalWebsiteUrl: TextEditingController(),
    phoneController: TextEditingController(),
    otpController: TextEditingController(),
    sePasswordFormKey: GlobalKey<FormState>(),
    createPasswordController: TextEditingController(),
    confirmPasswordController: TextEditingController(),
    aadharVerificationFormKey: GlobalKey<FormState>(),
    aadharNumberController: TextEditingController(),
    aadharOtpController: TextEditingController(),
    kartaAadharNumberController: TextEditingController(),
    kartaAadharOtpController: TextEditingController(),
    kartaAadharVerificationFormKey: GlobalKey<FormState>(),
    hufPanVerificationKey: GlobalKey<FormState>(),
    hufPanNumberController: TextEditingController(),
    isHUFPanVerifyingLoading: false,
    businessPanVerificationKey: GlobalKey<FormState>(),
    businessPanNumberController: TextEditingController(),
    businessPanNameController: TextEditingController(),
    directorsPanVerificationKey: GlobalKey<FormState>(),
    director1PanNumberController: TextEditingController(),
    director1PanNameController: TextEditingController(),
    director2PanNumberController: TextEditingController(),
    director2PanNameController: TextEditingController(),
    beneficialOwnerPanVerificationKey: GlobalKey<FormState>(),
    beneficialOwnerPanNumberController: TextEditingController(),
    beneficialOwnerPanNameController: TextEditingController(),
    businessRepresentativeFormKey: GlobalKey<FormState>(),
    businessRepresentativePanNumberController: TextEditingController(),
    businessRepresentativePanNameController: TextEditingController(),
    selectedCountry: Country(
      phoneCode: '91',
      countryCode: 'IN',
      e164Sc: 0,
      geographic: true,
      level: 1,
      name: 'India',
      example: '9123456789',
      displayName: 'India',
      displayNameNoCountryCode: 'India',
      e164Key: '',
    ),
    registerAddressFormKey: GlobalKey<FormState>(),
    pinCodeController: TextEditingController(),
    stateNameController: TextEditingController(),
    cityNameController: TextEditingController(),
    address1NameController: TextEditingController(),
    address2NameController: TextEditingController(),
    iceVerificationKey: GlobalKey<FormState>(),
    iceNumberController: TextEditingController(),
    cinVerificationKey: GlobalKey<FormState>(),
    cinNumberController: TextEditingController(),
    llpinNumberController: TextEditingController(),
    bankAccountVerificationFormKey: GlobalKey<FormState>(),
    bankAccountNumberController: TextEditingController(),
    reEnterbankAccountNumberController: TextEditingController(),
    ifscCodeController: TextEditingController(),
    directorCaptchaInputController: TextEditingController(),
    kartaCaptchaInputController: TextEditingController(),
    partnerAadharNumberController: TextEditingController(),
    partnerAadharOtpController: TextEditingController(),
    partnerAadharVerificationFormKey: GlobalKey<FormState>(),
    partnerCaptchaInputController: TextEditingController(),
    proprietorAadharNumberController: TextEditingController(),
    proprietorAadharOtpController: TextEditingController(),
    proprietorAadharVerificationFormKey: GlobalKey<FormState>(),
    proprietorCaptchaInputController: TextEditingController(),
    directorEmailIdNumberController: TextEditingController(),
    directorMobileNumberController: TextEditingController(),
    directorContactInformationKey: GlobalKey<FormState>(),
    otherDirectorsPanVerificationKey: GlobalKey<FormState>(),
    otherDirectorVerificationFormKey: GlobalKey<FormState>(),
    otherDirectorAadharNumberController: TextEditingController(),
    otherDirectoraadharOtpController: TextEditingController(),
    otherDirectorCaptchaInputController: TextEditingController(),
  );
}

void main() {
  // In-memory storage for testing
  final Map<String, String> mockStorage = {};

  setUpAll(() {
    // Initialize Flutter binding for tests
    TestWidgetsFlutterBinding.ensureInitialized();

    // Mock flutter_secure_storage plugin
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'write':
            final String key = methodCall.arguments['key'];
            final String value = methodCall.arguments['value'];
            mockStorage[key] = value;
            return null;
          case 'read':
            final String key = methodCall.arguments['key'];
            return mockStorage[key];
          case 'delete':
            final String key = methodCall.arguments['key'];
            mockStorage.remove(key);
            return null;
          case 'deleteAll':
            mockStorage.clear();
            return null;
          case 'readAll':
            return Map<String, String>.from(mockStorage);
          case 'containsKey':
            final String key = methodCall.arguments['key'];
            return mockStorage.containsKey(key);
          default:
            return null;
        }
      },
    );

    // Load test environment variables
    dotenv.testLoad(
      fileInput: '''
ENCRYPT_KEY=****************
ENCRYPT_IV=****************
''',
    );
  });

  tearDownAll(() {
    // Clean up the mock method channel handler
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'),
      null,
    );
  });

  group('AnnualTurnoverView Widget Tests', () {
    late MockBusinessAccountSetupBloc mockBloc;

    setUp(() async {
      mockBloc = MockBusinessAccountSetupBloc();

      // Clear mock storage between tests
      mockStorage.clear();

      // Set up default mock user data for LocalStorage with business nature
      const mockUserData = '{"business_details": {"business_nature": "Export of goods"}}';
      await Prefobj.preferences.put(Prefkeys.userDetail, mockUserData);

      // Also set up SharedPreferences mock for web compatibility
      SharedPreferences.setMockInitialValues({});
    });

    Widget createTestWidget() {
      return MaterialApp(
        localizationsDelegates: const [
          Lang.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: Lang.delegate.supportedLocales,
        home: Scaffold(
          body: BlocProvider<BusinessAccountSetupBloc>.value(value: mockBloc, child: const AnnualTurnoverView()),
        ),
      );
    }

    testWidgets('should render without errors', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState());
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState()]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byType(AnnualTurnoverView), findsOneWidget);
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(ScrollConfiguration), findsWidgets);
    });

    testWidgets('should display BlocBuilder', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState());
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState()]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byType(BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>), findsWidgets);
    });

    testWidgets('should display form with all required fields', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(
        selectedAnnualTurnover: "Less than ₹40 lakhs",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true, // Set GST as verified to show file upload widget
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      expect(find.byType(Form), findsOneWidget);
      expect(find.byType(CustomTileWidget), findsNWidgets(2)); // Two turnover options
      expect(find.byType(CustomTextInputField), findsOneWidget);
      expect(find.byType(CustomFileUploadWidget), findsOneWidget);
    });

    testWidgets('should display turnover title and description', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState());
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState()]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.textContaining('turnover'), findsAtLeastNWidgets(1));
      expect(find.textContaining('GST'), findsAtLeastNWidgets(1));
    });

    testWidgets('should display GST optional title when GST is not mandatory', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState(isGstCertificateMandatory: false));
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState(isGstCertificateMandatory: false)]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.textContaining('GST'), findsAtLeastNWidgets(1));
    });

    testWidgets('should display GST required title when GST is mandatory', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState(isGstCertificateMandatory: true));
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState(isGstCertificateMandatory: true)]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.textContaining('GST'), findsAtLeastNWidgets(1));
    });

    testWidgets('should display GST number field with proper validation', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(selectedAnnualTurnover: "Less than ₹40 lakhs");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      expect(find.textContaining('GST'), findsAtLeastNWidgets(1));
      expect(find.byType(CustomTextInputField), findsOneWidget);

      final gstField = find.byType(CustomTextInputField);
      final gstWidget = tester.widget<CustomTextInputField>(gstField);
      expect(gstWidget.maxLength, 15);
      expect(gstWidget.textInputAction, TextInputAction.done);
    });

    testWidgets('should display file upload widget for GST certificate', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(selectedAnnualTurnover: "Less than ₹40 lakhs");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      expect(find.byType(CustomFileUploadWidget), findsOneWidget);
      expect(find.textContaining('Certificate'), findsAtLeastNWidgets(1));
    });

    testWidgets('should show next button when GST is not mandatory', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(isGstCertificateMandatory: false, selectedAnnualTurnover: "Less than ₹20 lakhs");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      expect(find.byType(CustomElevatedButton), findsOneWidget); // Only Next button
    });

    testWidgets('should show next button when GST is mandatory', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(isGstCertificateMandatory: true, selectedAnnualTurnover: "₹40 lakhs or more");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      expect(find.byType(CustomElevatedButton), findsOneWidget); // Only Next button
    });

    testWidgets('should disable Next button when required fields are empty', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(selectedAnnualTurnover: "₹40 lakhs or more", isGstCertificateMandatory: true);
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isDisabled, true);
    });

    testWidgets('should enable Next button when all required fields are filled', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: '22AAAAA0000A1Z5',
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isDisabled, false);
    });

    testWidgets('should show loading state on Next button', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: '22AAAAA0000A1Z5',
        isGstVerificationLoading: true,
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true,
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pump(); // Additional pump for loading state

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isLoading, true);
    });

    testWidgets('should trigger AnnualTurnOverVerificationSubmitted event on Next button tap', (
      WidgetTester tester,
    ) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: '22AAAAA0000A1Z5',
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true,
      );

      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Scroll to make the button visible
      await tester.scrollUntilVisible(
        find.byType(CustomElevatedButton),
        500.0,
        scrollable: find.byType(Scrollable).first,
      );

      final nextButton = find.byType(CustomElevatedButton);
      await tester.tap(nextButton, warnIfMissed: false);
      await tester.pump();

      // Assert
      verify(mockBloc.add(any)).called(greaterThan(0));
    });

    testWidgets(
      'should trigger KycStepChanged event when Next button is tapped with empty fields (skip functionality)',
      (WidgetTester tester) async {
        // Arrange
        final state = createTestState(
          isGstCertificateMandatory: false,
          selectedAnnualTurnover: "Less than ₹20 lakhs", // This makes GST not mandatory
        );
        when(mockBloc.state).thenReturn(state);
        when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Let the FutureBuilder complete
        await tester.pumpAndSettle(); // Wait for all animations and async operations

        // Scroll to make the button visible
        await tester.scrollUntilVisible(
          find.byType(CustomElevatedButton),
          500.0,
          scrollable: find.byType(Scrollable).first,
        );

        final nextButton = find.byType(CustomElevatedButton);
        await tester.tap(nextButton, warnIfMissed: false);
        await tester.pump();

        // Assert
        verify(mockBloc.add(any)).called(greaterThan(0));
      },
    );

    testWidgets('should trigger UploadGstCertificateFile event on file selection', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true, // This is needed to show the file upload widget
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Find the file upload widget and trigger onFileSelected
      final fileUploadWidget = find.byType(CustomFileUploadWidget);
      expect(fileUploadWidget, findsOneWidget);
      final widget = tester.widget<CustomFileUploadWidget>(fileUploadWidget);

      // Simulate file selection
      final testFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      widget.onFileSelected?.call(testFile);
      await tester.pump();

      // Assert
      verify(mockBloc.add(any)).called(greaterThan(0));
    });

    testWidgets('should display correct responsive layout for mobile', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(isGstCertificateMandatory: false, selectedAnnualTurnover: "Less than ₹20 lakhs");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pump(); // Additional pump for layout

      // Assert - Mobile layout should have basic layout widgets
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(Column), findsWidgets);
      // Remove Row expectation as it may not exist in this specific layout
    });

    testWidgets('should handle form validation on Next button tap', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: 'INVALID_GST', // Invalid GST format
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true,
      );

      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pump(); // Additional pump for layout

      // Scroll to make the button visible
      await tester.scrollUntilVisible(
        find.byType(CustomElevatedButton),
        500.0,
        scrollable: find.byType(Scrollable).first,
      );

      final nextButton = find.byType(CustomElevatedButton);
      await tester.tap(nextButton, warnIfMissed: false);
      await tester.pump();

      // Assert - Form validation should prevent event from being triggered
      // The exact behavior depends on form validation implementation
    });

    testWidgets('should display turnover field with CustomTileWidget', (WidgetTester tester) async {
      // Arrange
      final state = createTestState();
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      expect(find.byType(CustomTileWidget), findsNWidgets(2)); // Two turnover options
      final tileWidgets = find.byType(CustomTileWidget);
      final firstWidget = tester.widget<CustomTileWidget>(tileWidgets.first);
      expect(firstWidget.title, 'Less than ₹40 lakhs');
      expect(firstWidget.isSelected, false); // No selection by default
      expect(firstWidget.showTextField, false); // Default value
    });

    testWidgets('should handle AnimatedBuilder for Next button state changes', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState());
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState()]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byType(AnimatedBuilder), findsWidgets);
    });

    testWidgets('should handle edge case with null isGstVerificationLoading', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: '22AAAAA0000A1Z5',
        isGstVerificationLoading: null,
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true,
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isLoading, false); // Should default to false when null
    });

    testWidgets('should handle partial field completion - only turnover filled', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(
        turnoverText: '1000000',
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isDisabled, true); // Should be disabled as GST number and file are missing
    });

    testWidgets('should handle partial field completion - only GST number filled', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(
        gstNumberText: '22AAAAA0000A1Z5',
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true, // This is needed to show the Next button
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isDisabled, true); // Should be disabled as GST file is missing when GST is mandatory
    });

    testWidgets('should handle partial field completion - only file uploaded', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true,
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isDisabled, false); // Should be enabled as GST file is uploaded and GST is mandatory
    });

    testWidgets('should handle Next button when at last KYC step', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(
        isGstCertificateMandatory: false,
        currentKycVerificationStep: KycVerificationSteps.bankAccountLinking, // Last step
        selectedAnnualTurnover: "Less than ₹20 lakhs",
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pump(); // Additional pump for layout

      // Scroll to make the button visible
      await tester.scrollUntilVisible(
        find.byType(CustomElevatedButton),
        500.0,
        scrollable: find.byType(Scrollable).first,
      );

      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      await tester.tap(nextButton, warnIfMissed: false);
      await tester.pump();

      // Assert - Should handle the last step properly
      expect(find.byType(CustomElevatedButton), findsOneWidget);
    });

    testWidgets('should display correct text styles and responsive fonts', (WidgetTester tester) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState());
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState()]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert - Test that text widgets are rendered with proper styling
      expect(find.byType(Text), findsWidgets);
    });

    testWidgets('should test _buildSelectionTitleAndDescription method with different parameters', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockBloc.state).thenReturn(createTestState(isGstCertificateMandatory: true));
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([createTestState(isGstCertificateMandatory: true)]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert - This tests the _buildSelectionTitleAndDescription method with required GST
      expect(find.textContaining('GST'), findsAtLeastNWidgets(1));
    });

    testWidgets('should handle Listenable.merge in AnimatedBuilder', (WidgetTester tester) async {
      // Arrange
      final state = createTestState();
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Simulate text changes to trigger AnimatedBuilder
      state.turnOverController.text = '500000';
      state.gstNumberController.text = '22AAAAA0000A1Z5';
      await tester.pump();

      // Assert
      expect(find.byType(AnimatedBuilder), findsWidgets);
    });

    testWidgets('should handle tooltip message on Next button', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: '22AAAAA0000A1Z5',
        selectedAnnualTurnover: "₹40 lakhs or more",
        isGstCertificateMandatory: true,
        isGSTNumberVerify: true,
      );
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert
      final nextButton = find.byType(CustomElevatedButton);
      expect(nextButton, findsOneWidget);
      final button = tester.widget<CustomElevatedButton>(nextButton);
      expect(button.isShowTooltip, true);
    });

    testWidgets('should handle button width for web vs mobile', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(isGstCertificateMandatory: false, selectedAnnualTurnover: "Less than ₹20 lakhs");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert - Test button width configuration
      final buttons = find.byType(CustomElevatedButton);
      expect(buttons, findsOneWidget); // Only Next button exists
    });

    testWidgets('should handle empty onTap callback in CustomTileWidget', (WidgetTester tester) async {
      // Arrange
      final state = createTestState();
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Find and tap the first CustomTileWidget
      final tileWidgets = find.byType(CustomTileWidget);
      expect(tileWidgets, findsNWidgets(2));
      await tester.tap(tileWidgets.first);
      await tester.pump();

      // Assert - Should not cause any errors
      expect(find.byType(CustomTileWidget), findsNWidgets(2));
    });

    testWidgets('should display web layout when on web platform', (WidgetTester tester) async {
      // Arrange
      final state = createTestState(isGstCertificateMandatory: false, selectedAnnualTurnover: "Less than ₹20 lakhs");
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert - Test web-specific layout elements
      expect(find.byType(Column), findsWidgets); // Column widgets should exist
      expect(find.byType(Container), findsWidgets); // Container widgets should exist
    });

    testWidgets('should handle controller text changes', (WidgetTester tester) async {
      // Arrange
      final state = createTestState();
      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Simulate controller text changes
      state.turnOverController.text = 'test';
      state.gstNumberController.text = 'test';
      await tester.pump();

      // Assert
      expect(state.turnOverController.text, 'test');
      expect(state.gstNumberController.text, 'test');
    });

    testWidgets('should test all button states and interactions', (WidgetTester tester) async {
      // Arrange
      final gstFile = FileData(name: 'gst.pdf', bytes: Uint8List(0), sizeInMB: 1.0);
      final state = createTestState(
        gstCertificateFile: gstFile,
        turnoverText: '1000000',
        gstNumberText: '22AAAAA0000A1Z5',
        isGstCertificateMandatory: false,
        selectedAnnualTurnover: "Less than ₹20 lakhs",
        isGSTNumberVerify: true,
      );

      when(mockBloc.state).thenReturn(state);
      when(mockBloc.stream).thenAnswer((_) => Stream.fromIterable([state]));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Let the FutureBuilder complete
      await tester.pumpAndSettle(); // Wait for all animations and async operations

      // Assert - Test that button is present and functional
      expect(find.byType(CustomElevatedButton), findsOneWidget);
      expect(find.textContaining('Next'), findsOneWidget);
    });
  });
}
