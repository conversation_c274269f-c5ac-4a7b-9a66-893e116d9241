import 'package:exchek/repository/personal_user_kyc_repository.dart';
import 'package:exchek/core/api_config/client/api_client.dart';
import 'package:exchek/models/auth_models/common_success_model.dart';
import 'package:exchek/models/personal_user_models/get_city_and_state_model.dart';
import 'package:exchek/models/personal_user_models/get_pan_detail_model.dart';
import 'package:exchek/models/personal_user_models/get_gst_details_model.dart';
import 'package:exchek/widgets/common_widget/app_file_upload_widget.dart';
import 'package:exchek/models/personal_user_models/aadhar_otp_model.dart';
import 'package:exchek/models/personal_user_models/aadhar_verify_otp_model.dart';
import 'package:exchek/models/personal_user_models/captcha_model.dart';
import 'package:exchek/models/personal_user_models/recaptcha_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:exchek/core/flavor_config/env_config.dart';
import 'package:exchek/core/enums/app_enums.dart';
import 'package:exchek/core/flavor_config/flavor_config.dart';

import 'personal_user_kyc_repository_test.mocks.dart';

@GenerateMocks([ApiClient, FileData])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() {
    // Initialize FlavorConfig for testing
    const testBaseUrl = 'https://test-api.example.com';
    final testEnvConfig = EnvConfig(baseUrl: testBaseUrl);
    FlavorConfig.initialize(flavor: Flavor.dev, env: testEnvConfig);
  });

  late MockApiClient mockApiClient;
  late PersonalUserKycRepository repository;
  late FileData mockFileData;

  setUp(() {
    mockApiClient = MockApiClient();
    repository = PersonalUserKycRepository(apiClient: mockApiClient);
    mockFileData = MockFileData();
  });

  group('uploadPersonalKyc', () {
    test('success', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});
      final result = await repository.uploadPersonalKyc(
        userID: '1',
        documentType: 'PAN',
        documentNumber: '**********',
        nameOnPan: 'Test User',
        documentFrontImage: mockFileData,
        documentBackImage: mockFileData,
        isAddharCard: true,
        userType: "personal",
      );
      expect(result, isA<CommonSuccessModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).thenThrow(Exception('error'));
      expect(
        () => repository.uploadPersonalKyc(
          userID: '1',
          documentType: 'PAN',
          documentNumber: '**********',
          nameOnPan: 'Test User',
          documentFrontImage: mockFileData,
          documentBackImage: mockFileData,
          isAddharCard: true,
          userType: 'personal',
        ),
        throwsException,
      );
    });
  });

  group('generateCaptcha', () {
    test('success', () async {
      when(mockApiClient.request(any, any)).thenAnswer((_) async => <String, dynamic>{'captcha': '1234'});
      final result = await repository.generateCaptcha();
      expect(result, isA<CaptchaModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any)).thenThrow(Exception('error'));
      expect(repository.generateCaptcha(), throwsException);
    });
  });

  group('reGenerateCaptcha', () {
    test('success', () async {
      when(mockApiClient.request(any, any)).thenAnswer(
        (_) async => {
          'timestamp': 1234567890,
          'transaction_id': 'txn123',
          'data': {'captcha': 'abcd'},
          'code': 200,
        },
      );
      final result = await repository.reGenerateCaptcha(sessionId: 'session');
      expect(result, isA<RecaptchaModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any)).thenThrow(Exception('error'));
      expect(repository.reGenerateCaptcha(sessionId: 'session'), throwsException);
    });
  });

  group('generateAadharOTP', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'code': 200,
          'timestamp': 1234567890,
          'transaction_id': 'txn123',
          'sub_code': 'sub1',
          'message': 'OTP sent',
        },
      );
      final result = await repository.generateAadharOTP(
        aadhaarNumber: '123412341234',
        captcha: 'abcd',
        sessionId: 'session',
      );
      expect(result, isA<AadharOTPSendModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('error'));
      expect(
        repository.generateAadharOTP(aadhaarNumber: '123412341234', captcha: 'abcd', sessionId: 'session'),
        throwsException,
      );
    });
  });

  group('validateAadharOtp', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'code': 200,
          'timestamp': 1234567890,
          'transaction_id': 'txn123',
          'sub_code': 'sub1',
          'message': 'OTP verified',
          'data': {
            'address': {
              'care_of': 'c/o',
              'country': 'IN',
              'district': 'Dist',
              'house': '123',
              'landmark': 'Landmark',
              'locality': 'Local',
              'pin': '123456',
              'post_office': 'PO',
              'state': 'State',
              'street': 'Street',
              'sub_district': 'SubDist',
              'vtc': 'VTC',
            },
            'date_of_birth': '1990-01-01',
            'email': '<EMAIL>',
            'gender': 'M',
            'generated_at': 'now',
            'masked_number': 'XXXX1234',
            'name': 'Test Name',
            'phone': '9999999999',
            'photo': 'base64',
          },
        },
      );
      final result = await repository.validateAadharOtp(faker: true, otp: '123456', sessionId: 'session', userId: '1');
      expect(result, isA<AadharOTPVerifyModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('error'));
      expect(
        repository.validateAadharOtp(faker: true, otp: '123456', sessionId: 'session', userId: '1'),
        throwsException,
      );
    });
  });

  group('getPanDetails', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'success': true,
          'data': {
            '@entity': 'entity',
            'pan': '**********',
            'full_name': 'Test User',
            'status': 'Active',
            'category': 'Individual',
            'name_information': {'pan_name_cleaned': 'Test User'},
          },
        },
      );
      final result = await repository.getPanDetails(panNumber: '**********', userId: '123');
      expect(result, isA<GetPanDetailModel>());
      expect(result.success, isTrue);
      expect(result.data?.pan, equals('**********'));
      expect(result.data?.fullName, equals('Test User'));
      expect(result.data?.status, equals('Active'));
      expect(result.data?.category, equals('Individual'));
      expect(result.data?.nameInformation?.panNameCleaned, equals('Test User'));
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('error'));
      expect(repository.getPanDetails(panNumber: '**********', userId: '123'), throwsException);
    });
  });

  group('getCityAndState', () {
    test('success', () async {
      when(mockApiClient.request(any, any)).thenAnswer(
        (_) async => {
          'success': true,
          'data': {'city': 'TestCity', 'state': 'TestState'},
        },
      );
      final result = await repository.getCityAndState(pincode: '123456');
      expect(result, isA<GetCityAndStateModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any)).thenThrow(Exception('error'));
      expect(repository.getCityAndState(pincode: '123456'), throwsException);
    });
  });

  group('uploadResidentialAddressDetails', () {
    test('success', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});
      final result = await repository.uploadResidentialAddressDetails(
        userID: '1',
        userType: 'personal',
        country: 'IN',
        pinCode: '123456',
        state: 'TestState',
        city: 'TestCity',
        addressLine1: 'Line1',
        addressLine2: 'Line2',
        documentType: 'AADHAAR',
        documentFrontImage: mockFileData,
        documentBackImage: mockFileData,
        isAddharCard: true,
        aadhaarUsedAsIdentity: 'yes',
      );
      expect(result, isA<CommonSuccessModel>());
    });
    test('throws error', () async {
      when(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).thenThrow(Exception('error'));
      expect(
        repository.uploadResidentialAddressDetails(
          userID: '1',
          userType: 'personal',
          country: 'IN',
          pinCode: '123456',
          state: 'TestState',
          city: 'TestCity',
          addressLine1: 'Line1',
          addressLine2: 'Line2',
          documentType: 'AADHAAR',
          documentFrontImage: mockFileData,
          documentBackImage: mockFileData,
          isAddharCard: true,
          aadhaarUsedAsIdentity: 'yes',
        ),
        throwsException,
      );
    });
  });

  group('getGSTDetails', () {
    test('success', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenAnswer(
        (_) async => {
          'success': true,
          'data': {'legal_name': 'Test Company', 'message': 'GST details found', 'status': 'Active'},
        },
      );
      final result = await repository.getGSTDetails(
        userID: '1',
        estimatedAnnualIncome: '1000000',
        gstNumber: '12ABCDE3456F7Z8',
      );
      expect(result, isA<GetGstDetailsModel>());
      expect(result.success, isTrue);
      expect(result.data?.legalName, equals('Test Company'));
      expect(result.data?.message, equals('GST details found'));
      expect(result.data?.status, equals('Active'));


    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, data: anyNamed('data'))).thenThrow(Exception('error'));
      expect(
        repository.getGSTDetails(userID: '1', estimatedAnnualIncome: '1000000', gstNumber: '12ABCDE3456F7Z8'),
        throwsException,
      );
    });
  });

  group('uploadGSTDocument', () {
    test('success', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});
      final result = await repository.uploadGSTDocument(
        userID: '1',
        gstNumber: '12ABCDE3456F7Z8',
        userType: 'business',
        gstCertificate: mockFileData,
      );
      expect(result, isA<CommonSuccessModel>());
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).thenThrow(Exception('error'));
      expect(
        repository.uploadGSTDocument(
          userID: '1',
          gstNumber: '12ABCDE3456F7Z8',
          userType: 'business',
          gstCertificate: mockFileData,
        ),
        throwsException,
      );
    });
  });

  group('uploadBusinessLegalDocuments', () {
    test('success', () async {
      when(
        mockApiClient.request(any, any, multipartData: anyNamed('multipartData')),
      ).thenAnswer((_) async => <String, dynamic>{'success': true});
      final result = await repository.uploadBusinessLegalDocuments(
        userID: '1',
        userType: 'business',
        documentType: 'INCORPORATION_CERTIFICATE',
        documentNumber: 'INC123456',
        documentFrontImage: mockFileData,
      );
      expect(result, isA<CommonSuccessModel>());
    });

    test('throws error', () async {
      when(mockApiClient.request(any, any, multipartData: anyNamed('multipartData'))).thenThrow(Exception('error'));
      expect(
        repository.uploadBusinessLegalDocuments(
          userID: '1',
          userType: 'business',
          documentType: 'INCORPORATION_CERTIFICATE',
          documentNumber: 'INC123456',
          documentFrontImage: mockFileData,
        ),
        throwsException,
      );
    });
  });
}
