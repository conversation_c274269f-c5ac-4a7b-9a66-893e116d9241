import 'package:exchek/models/auth_models/get_user_detail_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('GetUserDetailModel', () {
    test('constructor creates instance with required parameters', () {
      final data = Data(
        userId: 'user123',
        userEmail: '<EMAIL>',
        userType: 'personal',
        mobileNumber: '1234567890',
        multicurrency: ['USD', 'EUR'],
        estimatedMonthlyVolume: '10000',
      );

      final model = GetUserDetailModel(success: true, data: data);

      expect(model.success, isTrue);
      expect(model.data, equals(data));
    });

    test('from<PERSON><PERSON> creates instance from JSON with business details', () {
      final json = {
        'success': true,
        'data': {
          'business_details': {
            'business_legal_name': 'Test Company Ltd',
            'business_nature': 'Technology',
            'business_type': 'Private Limited',
            'exports_type': ['Services'],
          },
          'user_id': 'user123',
          'user_email': '<EMAIL>',
          'user_type': 'business',
          'mobile_number': '9876543210',
          'multicurrency': ['USD', 'EUR', 'GBP'],
          'estimated_monthly_volume': '50000',
          'personal_details': null,
        },
      };

      final model = GetUserDetailModel.fromJson(json);

      expect(model.success, isTrue);
      expect(model.data?.userId, equals('user123'));
      expect(model.data?.userEmail, equals('<EMAIL>'));
      expect(model.data?.userType, equals('business'));
      expect(model.data?.mobileNumber, equals('9876543210'));
      // expect(model.data.createdAt, equals(DateTime.parse('2023-01-01T00:00:00.000Z')));
      // expect(model.data.updatedAt, equals(DateTime.parse('2023-01-02T00:00:00.000Z')));
      expect(model.data?.multicurrency, equals(['USD', 'EUR', 'GBP']));
      expect(model.data?.estimatedMonthlyVolume, equals('50000'));
      expect(model.data?.businessDetails, isNotNull);
      expect(model.data?.businessDetails!.businessLegalName, equals('Test Company Ltd'));
      expect(model.data?.personalDetails, isNull);
    });

    test('fromJson creates instance from JSON with personal details', () {
      final json = {
        'success': false,
        'data': {
          'business_details': null,
          'user_id': 'user456',
          'user_email': '<EMAIL>',
          'user_type': 'personal',
          'mobile_number': '5555555555',
          'multicurrency': ['INR'],
          'estimated_monthly_volume': '5000',
          'personal_details': {
            'payment_purpose': 'Investment',
            'profession': ['Software Engineer'],
            'product_desc': 'Freelance Services',
            'legal_full_name': 'John Doe',
          },
        },
      };

      final model = GetUserDetailModel.fromJson(json);

      expect(model.success, isFalse);
      expect(model.data?.userId, equals('user456'));
      expect(model.data?.userEmail, equals('<EMAIL>'));
      expect(model.data?.userType, equals('personal'));
      expect(model.data?.mobileNumber, equals('5555555555'));
      expect(model.data?.multicurrency, equals(['INR']));
      expect(model.data?.estimatedMonthlyVolume, equals('5000'));
      expect(model.data?.businessDetails, isNull);
      expect(model.data?.personalDetails, isNotNull);
      expect(model.data?.personalDetails!.paymentPurpose, equals('Investment'));
      expect(model.data?.personalDetails!.profession, equals(['Software Engineer']));
    });

    test('toJson converts instance to JSON with business details', () {
      final businessDetails = BusinessDetails(
        businessLegalName: 'ABC Corp',
        businessNature: 'Manufacturing',
        businessType: 'Corporation',
        exportsType: ['Goods'],
      );

      final data = Data(
        businessDetails: businessDetails,
        userId: 'biz123',
        userEmail: '<EMAIL>',
        userType: 'business',
        mobileNumber: '1111111111',
        multicurrency: ['USD', 'CAD'],
        estimatedMonthlyVolume: '75000',
      );

      final model = GetUserDetailModel(success: true, data: data);
      final json = model.toJson();

      expect(json['success'], isTrue);
      expect(json['data']['user_id'], equals('biz123'));
      expect(json['data']['user_email'], equals('<EMAIL>'));
      expect(json['data']['user_type'], equals('business'));
      expect(json['data']['mobile_number'], equals('1111111111'));
      expect(json['data']['multicurrency'], equals(['USD', 'CAD']));
      expect(json['data']['estimated_monthly_volume'], equals('75000'));
      expect(json['data']['business_details'], isNotNull);
      expect(json['data']['business_details']['business_legal_name'], equals('ABC Corp'));
      expect(json['data']['business_details']['exports_type'], equals(['Goods']));
      expect(json['data'].containsKey('personal_details'), isFalse);
    });

    test('toJson converts instance to JSON with personal details', () {
      final personalDetails = PersonalDetails(
        paymentPurpose: 'Education',
        profession: ['Teacher'],
        productDesc: 'Online Courses',
        legalFullName: 'Jane Smith',
      );

      final data = Data(
        personalDetails: personalDetails,
        userId: 'per789',
        userEmail: '<EMAIL>',
        userType: 'personal',
        mobileNumber: '2222222222',
        multicurrency: ['EUR', 'GBP'],
        estimatedMonthlyVolume: '3000',
      );

      final model = GetUserDetailModel(success: false, data: data);
      final json = model.toJson();

      expect(json['success'], isFalse);
      expect(json['data']['user_id'], equals('per789'));
      expect(json['data']['user_email'], equals('<EMAIL>'));
      expect(json['data']['user_type'], equals('personal'));
      expect(json['data']['mobile_number'], equals('2222222222'));
      expect(json['data']['multicurrency'], equals(['EUR', 'GBP']));
      expect(json['data']['estimated_monthly_volume'], equals('3000'));
      expect(json['data']['personal_details'], isNotNull);
      expect(json['data']['personal_details']['payment_purpose'], equals('Education'));
      expect(json['data']['personal_details']['profession'], equals(['Teacher']));
      expect(json['data']['personal_details']['legal_full_name'], equals('Jane Smith'));
      expect(json['data'].containsKey('business_details'), isFalse);
    });

    test('toJson converts instance to JSON without optional details', () {
      final data = Data(
        userId: 'min123',
        userEmail: '<EMAIL>',
        userType: 'basic',
        mobileNumber: '3333333333',
        multicurrency: ['USD'],
        estimatedMonthlyVolume: '1000',
      );

      final model = GetUserDetailModel(success: true, data: data);
      final json = model.toJson();

      expect(json['success'], isTrue);
      expect(json['data']['user_id'], equals('min123'));
      expect(json['data'].containsKey('business_details'), isFalse);
      expect(json['data'].containsKey('personal_details'), isFalse);
    });
  });

  group('Data', () {
    test('constructor creates instance with all parameters', () {
      final businessDetails = BusinessDetails(
        businessLegalName: 'Test Business',
        businessNature: 'Service',
        businessType: 'LLC',
        exportsType: ['Digital'],
      );

      final personalDetails = PersonalDetails(
        paymentPurpose: 'Business',
        profession: ['Consultant'],
        productDesc: 'Consulting Services',
        legalFullName: 'Test User',
      );

      final data = Data(
        businessDetails: businessDetails,
        userId: 'test123',
        userEmail: '<EMAIL>',
        userType: 'hybrid',
        mobileNumber: '4444444444',
        multicurrency: ['USD', 'EUR', 'GBP', 'INR'],
        estimatedMonthlyVolume: '100000',
        personalDetails: personalDetails,
      );

      expect(data.businessDetails, equals(businessDetails));
      expect(data.userId, equals('test123'));
      expect(data.userEmail, equals('<EMAIL>'));
      expect(data.userType, equals('hybrid'));
      expect(data.mobileNumber, equals('4444444444'));
      expect(data.multicurrency, equals(['USD', 'EUR', 'GBP', 'INR']));
      expect(data.estimatedMonthlyVolume, equals('100000'));
      expect(data.personalDetails, equals(personalDetails));
    });
  });

  group('BusinessDetails', () {
    test('constructor creates instance with required parameters', () {
      final businessDetails = BusinessDetails(
        businessLegalName: 'XYZ Corporation',
        businessNature: 'Technology Services',
        businessType: 'Public Limited Company',
        exportsType: ['Software Products'],
      );

      expect(businessDetails.businessLegalName, equals('XYZ Corporation'));
      expect(businessDetails.businessNature, equals('Technology Services'));
      expect(businessDetails.businessType, equals('Public Limited Company'));
      expect(businessDetails.exportsType, equals(['Software Products']));
    });

    test('fromJson creates instance from JSON', () {
      final json = {
        'business_legal_name': 'Tech Innovations Ltd',
        'business_nature': 'Software Development',
        'business_type': 'Private Limited',
        'exports_type': ['IT Services'],
      };

      final businessDetails = BusinessDetails.fromJson(json);

      expect(businessDetails.businessLegalName, equals('Tech Innovations Ltd'));
      expect(businessDetails.businessNature, equals('Software Development'));
      expect(businessDetails.businessType, equals('Private Limited'));
      expect(businessDetails.exportsType, equals(['IT Services']));
    });

    test('toJson converts instance to JSON', () {
      final businessDetails = BusinessDetails(
        businessLegalName: 'Global Trade Co',
        businessNature: 'Import Export',
        businessType: 'Partnership',
        exportsType: ['Physical Goods'],
      );

      final json = businessDetails.toJson();

      expect(json['business_legal_name'], equals('Global Trade Co'));
      expect(json['business_nature'], equals('Import Export'));
      expect(json['business_type'], equals('Partnership'));
      expect(json['exports_type'], equals(['Physical Goods']));
    });
  });

  group('PersonalDetails', () {
    test('constructor creates instance with required parameters', () {
      final personalDetails = PersonalDetails(
        paymentPurpose: 'Personal Investment',
        profession: ['Doctor'],
        productDesc: 'Medical Services',
        legalFullName: 'Dr. Alice Johnson',
      );

      expect(personalDetails.paymentPurpose, equals('Personal Investment'));
      expect(personalDetails.profession, equals(['Doctor']));
      expect(personalDetails.productDesc, equals('Medical Services'));
      expect(personalDetails.legalFullName, equals('Dr. Alice Johnson'));
    });

    test('fromJson creates instance from JSON', () {
      final json = {
        'payment_purpose': 'Freelance Work',
        'profession': ['Graphic Designer'],
        'product_desc': 'Design Services',
        'legal_full_name': 'Bob Wilson',
      };

      final personalDetails = PersonalDetails.fromJson(json);

      expect(personalDetails.paymentPurpose, equals('Freelance Work'));
      expect(personalDetails.profession, equals(['Graphic Designer']));
      expect(personalDetails.productDesc, equals('Design Services'));
      expect(personalDetails.legalFullName, equals('Bob Wilson'));
    });

    test('toJson converts instance to JSON', () {
      final personalDetails = PersonalDetails(
        paymentPurpose: 'Online Business',
        profession: ['Content Creator'],
        productDesc: 'Digital Content',
        legalFullName: 'Charlie Brown',
      );

      final json = personalDetails.toJson();

      expect(json['payment_purpose'], equals('Online Business'));
      expect(json['profession'], equals(['Content Creator']));
      expect(json['product_desc'], equals('Digital Content'));
      expect(json['legal_full_name'], equals('Charlie Brown'));
    });
  });
}
