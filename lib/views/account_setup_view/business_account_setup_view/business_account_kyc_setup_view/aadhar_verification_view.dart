import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/account_setup_widgets/aadhar_upload_note.dart';
import 'package:exchek/widgets/account_setup_widgets/captcha_image.dart';

class AadharVerificationView extends StatelessWidget {
  const AadharVerificationView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            padding: EdgeInsetsGeometry.symmetric(
              horizontal: ResponsiveHelper.isMobile(context) ? (kIsWeb ? 30.0 : 20) : 0.0,
            ),
            child: Center(
              child: Container(
                constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxTileWidth(context)),
                padding: EdgeInsetsGeometry.symmetric(horizontal: ResponsiveHelper.isMobile(context) ? 30.0 : 0.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSizedBoxH(20.0),
                    _buildSelectionTitleAndDescription(
                      context: context,
                      title: Lang.of(context).lbl_director_aadhar_verification,
                      description: Lang.of(context).lbl_verification_aadhar_otp_content,
                    ),
                    buildSizedBoxH(30.0),
                    if (state.isAadharVerified == false)
                      _buildIsNotAadharVerify(context, state)
                    else
                      _buildIsAadharVerify(context, state),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildIsAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.aadharVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildVerifyAadharNumber(context, state),
          buildSizedBoxH(24.0),
          _uolpadAadhaarTitle(context),
          buildSizedBoxH(24.0),
          UploadNote(notes: [Lang.of(context).lbl_note_1, Lang.of(context).lbl_note_2]),
          buildSizedBoxH(24.0),
          _buildUploadAddharCard(context),
          buildSizedBoxH(30.0),
          _buildNextButton(),
        ],
      ),
    );
  }

  Widget _buildIsNotAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.aadharVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAadharNumberField(context, state),
          if (!state.isDirectorCaptchaSend && !state.isOtpSent) buildSizedBoxH(20),
          if (!state.isDirectorCaptchaSend && !state.isOtpSent) _buildVerifyAadharButton(context, state),
          if (state.isDirectorCaptchaSend) ...[
            buildSizedBoxH(24.0),
            Builder(
              builder: (context) {
                if (state.directorCaptchaImage != null) {
                  return Column(
                    children: [
                      Row(
                        spacing: 20.0,
                        children: [
                          Base64CaptchaField(base64Image: state.directorCaptchaImage ?? ''),
                          AbsorbPointer(
                            absorbing: state.isOtpSent,
                            child: Opacity(
                              opacity: state.isOtpSent ? 0.5 : 1.0,
                              child: CustomImageView(
                                imagePath: Assets.images.svgs.icons.icRefresh.path,
                                height: 40.0,
                                width: 40.0,
                                onTap: () async {
                                  context.read<BusinessAccountSetupBloc>().add(DirectorReCaptchaSend());
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      buildSizedBoxH(24.0),
                      _buildCaptchaField(context),
                      if (!state.isOtpSent) ...[buildSizedBoxH(24.0), _buildSendOTPButton(context, state)],
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ],
          if (state.isOtpSent) ...[
            buildSizedBoxH(24.0),
            _buildOTPField(context, state),
            if (state.isAadharOTPInvalidate.isNotEmpty) ...[
              buildSizedBoxH(10),
              Text(
                state.isAadharOTPInvalidate,
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xffFF5D5F),
                ),
              ),
            ],
            buildSizedBoxH(30.0),
            _buildVerifyButton(context, state),
          ],
        ],
      ),
    );
  }

  Widget _uolpadAadhaarTitle(BuildContext context) {
    return Text(
      Lang.of(context).lbl_upload_aadhar_card,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
        fontWeight: FontWeight.w500,
        letterSpacing: 0.32,
      ),
    );
  }

  Widget _buildSelectionTitleAndDescription({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
            fontWeight: FontWeight.w500,
            letterSpacing: 0.32,
          ),
        ),
        buildSizedBoxH(14.0),
        Text(
          description,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            color: Theme.of(context).customColors.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildAadharNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_aadhar_number,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.digits,
          controller: state.aadharNumberController,
          maxLength: 14,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            GroupedInputFormatter(groupSizes: [4, 4, 4], separator: '-', digitsOnly: false, toUpperCase: true),
          ],
          validator: ExchekValidations.validateAadhaar,
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(DirectorAadharNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            final isValidAadhar = state.aadharNumberController.text.trim().length == 14;
            if (isValidAadhar) {
              context.read<BusinessAccountSetupBloc>().add(DirectorCaptchaSend());
            }
          },
        ),
      ],
    );
  }

  Widget _buildSendOTPButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.aadharNumberController]),
        builder: (context, child) {
          final isValidAadhar = state.aadharNumberController.text.trim().length == 14;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_send_otp,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isLoading: state.isDirectorAadharOtpLoading,
            isDisabled: !isValidAadhar,
            borderRadius: 18.0,
            onPressed:
                isValidAadhar
                    ? () async {
                      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                      context.read<BusinessAccountSetupBloc>().add(
                        SendAadharOtp(
                          state.aadharNumberController.text.trim(),
                          state.directorCaptchaInputController.text.trim(),
                          sessionId,
                        ),
                      );
                    }
                    : null,
          );
        },
      ),
    );
  }

  Widget _buildOTPField(BuildContext context, BusinessAccountSetupState state) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Lang.of(context).lbl_OTP,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                fontWeight: FontWeight.w400,
                height: 1.22,
              ),
            ),
            buildSizedBoxH(8.0),
            CustomTextInputField(
              context: context,
              type: InputType.digits,
              controller: state.aadharOtpController,
              textInputAction: TextInputAction.done,
              validator: ExchekValidations.validateOTP,
              suffixText: true,
              suffixIcon: ValueListenableBuilder(
                valueListenable: state.aadharNumberController,
                builder: (context, _, __) {
                  return GestureDetector(
                    onTap:
                        state.isAadharOtpTimerRunning
                            ? null
                            : () async {
                              FocusManager.instance.primaryFocus?.unfocus();
                              final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                              BlocProvider.of<BusinessAccountSetupBloc>(context).add(
                                SendAadharOtp(
                                  state.aadharNumberController.text.trim(),
                                  state.directorCaptchaInputController.text.trim(),
                                  sessionId,
                                ),
                              );
                            },
                    child: Container(
                      color: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                      child: Text(
                        state.isAadharOtpTimerRunning
                            ? '${Lang.of(context).lbl_resend_otp_in} ${formatSecondsToMMSS(state.aadharOtpRemainingTime)}sec'
                            : Lang.of(context).lbl_resend_OTP,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              state.isAadharOtpTimerRunning || (state.aadharNumberController.text.trim().length != 14)
                                  ? Theme.of(context).customColors.textdarkcolor?.withValues(alpha: 0.5)
                                  : Theme.of(context).customColors.greenColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
              maxLength: 6,
              contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly, NoPasteFormatter()],
              onFieldSubmitted: (value) {
                if (state.aadharVerificationFormKey.currentState?.validate() ?? false) {
                  context.read<BusinessAccountSetupBloc>().add(
                    AadharNumbeVerified(state.aadharNumberController.text, state.aadharOtpController.text),
                  );
                }
              },
              contextMenuBuilder: (BuildContext context, EditableTextState editableTextState) {
                return AdaptiveTextSelectionToolbar.buttonItems(
                  anchors: editableTextState.contextMenuAnchors,
                  buttonItems:
                      editableTextState.contextMenuButtonItems
                          .where((item) => item.type != ContextMenuButtonType.paste)
                          .toList(),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.aadharOtpController, state.aadharNumberController]),
        builder: (context, child) {
          final isDisable = state.aadharOtpController.text.isEmpty || state.aadharOtpController.text.isEmpty;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isAadharVerifiedLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisable,
            borderRadius: 18.0,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.aadharVerificationFormKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          AadharNumbeVerified(state.aadharNumberController.text, state.aadharOtpController.text),
                        );
                      }
                    },
          );
        },
      ),
    );
  }

  Widget _buildVerifyAadharNumber(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_aadhar_number_verified,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
          ),
        ),
        buildSizedBoxH(8.0),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 14.0, vertical: 14.0),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.fillColor,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Theme.of(context).customColors.greenColor!),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  ((state.aadharNumber ?? "").contains("-"))
                      ? (state.aadharNumber ?? '')
                      : GroupedInputFormatter.format(
                        input: state.aadharNumber ?? '',
                        groupSizes: [4, 4, 4],
                        separator: '-',
                        digitsOnly: true,
                      ),
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 14, desktop: 14),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              CustomImageView(imagePath: Assets.images.svgs.icons.icShieldTick.path, height: 20.0),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUploadAddharCard(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomFileUploadWidget(
              selectedFile: state.frontSideAdharFile,
              title: Lang.of(context).lbl_front_side_of_aadhar,
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(FrontSlideAadharCardUpload(fileData));
              },
            ),
            buildSizedBoxH(24.0),
            CustomFileUploadWidget(
              selectedFile: state.backSideAdharFile,
              title: Lang.of(context).lbl_back_side_of_aadhar,
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(BackSlideAadharCardUpload(fileData));
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isButtonEnabled = state.frontSideAdharFile != null && state.backSideAdharFile != null;

        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).save_and_next,
            borderRadius: 18.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isDisabled: !isButtonEnabled,
            isLoading: state.isAadharFileUploading,
            onPressed:
                isButtonEnabled
                    ? () {
                      context.read<BusinessAccountSetupBloc>().add(
                        AadharFileUploadSubmitted(
                          frontAadharFileData: state.frontSideAdharFile!,
                          backAadharFileData: state.backSideAdharFile!,
                        ),
                      );
                    }
                    : null,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
          ),
        );
      },
    );
  }

  String formatSecondsToMMSS(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$secs';
  }

  Widget _buildCaptchaField(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Enter Captcha",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                    fontWeight: FontWeight.w400,
                    height: 1.22,
                  ),
                ),
                buildSizedBoxH(8.0),
                AbsorbPointer(
                  absorbing: state.isOtpSent && state.directorCaptchaInputController.text.isNotEmpty,
                  child: CustomTextInputField(
                    context: context,
                    type: InputType.text,
                    controller: state.directorCaptchaInputController,
                    textInputAction: TextInputAction.done,
                    contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                    onFieldSubmitted:
                        state.isDirectorCaptchaLoading == true
                            ? null
                            : (value) async {
                              final isCaptchaValid = state.directorCaptchaInputController.text.isNotEmpty;

                              if (isCaptchaValid) {
                                final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                                context.read<BusinessAccountSetupBloc>().add(
                                  SendAadharOtp(
                                    state.aadharNumberController.text.trim(),
                                    state.directorCaptchaInputController.text.trim(),
                                    sessionId,
                                  ),
                                );
                              }
                            },
                    validator: ExchekValidations.validateRequired,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyAadharButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.aadharNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validateAadhaar(state.aadharNumberController.text.replaceAll("-", "").trim()) != null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isDirectorCaptchaLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 18.0,
            onPressed:
                isDisabled
                    ? null
                    : () {
                      final isValidAadhar =
                          ExchekValidations.validateAadhaar(
                            state.aadharNumberController.text.replaceAll("-", "").trim(),
                          ) ==
                          null;
                      if (isValidAadhar) {
                        context.read<BusinessAccountSetupBloc>().add(DirectorCaptchaSend());
                      }
                    },
          );
        },
      ),
    );
  }
}
