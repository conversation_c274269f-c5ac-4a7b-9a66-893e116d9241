import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/account_setup_widgets/aadhar_upload_note.dart';
import 'package:exchek/widgets/account_setup_widgets/captcha_image.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';

class PartnerAadharVerificationView extends StatelessWidget {
  const PartnerAadharVerificationView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: ResponsiveHelper.isWebAndIsNotMobile(context) ? 50 : 20),
            child: Center(
              child: Container(
                constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxTileWidth(context)),
                padding: EdgeInsetsGeometry.symmetric(
                  horizontal: ResponsiveHelper.isMobile(context) ? (kIsWeb ? 30.0 : 20) : 0.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSizedBoxH(20.0),
                    _buildSelectionTitleAndDescription(
                      context: context,
                      title: "Verify Partner's Aadhaar",
                      description:
                          "Verify the partner's Aadhaar using OTP and upload a copy for identity confirmation and compliance.",
                    ),
                    buildSizedBoxH(30.0),
                    if (state.isPartnerAadharVerified == false)
                      _buildIsNotAadharVerify(context, state)
                    else
                      _buildIsAadharVerify(context, state),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildIsAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.partnerAadharVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildVerifyPartnerAadharNumber(context, state),
          buildSizedBoxH(24.0),
          _uolpadAadhaarTitle(context),
          buildSizedBoxH(24.0),
          UploadNote(notes: [Lang.of(context).lbl_note_1, Lang.of(context).lbl_note_2]),
          buildSizedBoxH(24.0),
          _buildUploadAddharCard(context),
          buildSizedBoxH(30.0),
          _buildNextButton(),
        ],
      ),
    );
  }

  Widget _uolpadAadhaarTitle(BuildContext context) {
    return Text(
      Lang.of(context).lbl_upload_aadhar_card,
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
        fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
        fontWeight: FontWeight.w600,
        letterSpacing: 0.32,
      ),
    );
  }

  Widget _buildIsNotAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.partnerAadharVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPartnerAadharNumberField(context, state),
          if (!state.isPartnerCaptchaSend && !state.isPartnerOtpSent) buildSizedBoxH(20),
          if (!state.isPartnerCaptchaSend && !state.isPartnerOtpSent) _buildVerifyAadharButton(context, state),
          if (state.isPartnerCaptchaSend) ...[
            buildSizedBoxH(24.0),
            Builder(
              builder: (context) {
                if (state.partnerCaptchaImage != null) {
                  return Column(
                    children: [
                      Row(
                        spacing: 20.0,
                        children: [
                          Base64CaptchaField(base64Image: state.partnerCaptchaImage ?? ''),
                          AbsorbPointer(
                            absorbing: state.isPartnerOtpSent,
                            child: Opacity(
                              opacity: state.isPartnerOtpSent ? 0.5 : 1.0,
                              child: CustomImageView(
                                imagePath: Assets.images.svgs.icons.icRefresh.path,
                                height: 40.0,
                                width: 40.0,
                                onTap: () async {
                                  context.read<BusinessAccountSetupBloc>().add(PartnerReCaptchaSend());
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      buildSizedBoxH(24.0),
                      _buildCaptchaField(context),
                      if (!state.isPartnerOtpSent) ...[buildSizedBoxH(24.0), _buildSendOTPButton(context, state)],
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ],
          if (state.isPartnerOtpSent) ...[
            buildSizedBoxH(24.0),
            _buildOTPField(context),
            if (state.partnerIsAadharOTPInvalidate != null) ...[
              buildSizedBoxH(10),
              Text(
                state.partnerIsAadharOTPInvalidate ?? '',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xffFF5D5F),
                ),
              ),
            ],
            buildSizedBoxH(30.0),
            _buildVerifyButton(context, state),
          ],
        ],
      ),
    );
  }

  Widget _buildCaptchaField(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Enter Captcha",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                    fontWeight: FontWeight.w400,
                    height: 1.22,
                  ),
                ),
                buildSizedBoxH(8.0),
                AbsorbPointer(
                  absorbing: state.isPartnerOtpSent,
                  child: CustomTextInputField(
                    context: context,
                    type: InputType.text,
                    controller: state.partnerCaptchaInputController,
                    textInputAction: TextInputAction.done,
                    contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                    onFieldSubmitted:
                        state.isPartnerAadharVerifiedLoading == true
                            ? null
                            : (value) async {
                              final isCaptchaValid = state.partnerCaptchaInputController.text.isNotEmpty;
                              if (isCaptchaValid) {
                                final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                                FocusManager.instance.primaryFocus?.unfocus();
                                context.read<BusinessAccountSetupBloc>().add(
                                  PartnerSendAadharOtp(
                                    aadhar: state.partnerAadharNumberController.text.trim(),
                                    captcha: state.partnerCaptchaInputController.text.trim(),
                                    sessionId: sessionId,
                                  ),
                                );
                              }
                            },
                    validator: ExchekValidations.validateRequired,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildSelectionTitleAndDescription({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
            fontWeight: FontWeight.w500,
            letterSpacing: 0.32,
          ),
        ),
        buildSizedBoxH(14.0),
        Text(
          description,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            color: Theme.of(context).customColors.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPartnerAadharNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Partner Aadhaar Number",
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.digits,
          controller: state.partnerAadharNumberController,
          maxLength: 14,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            GroupedInputFormatter(groupSizes: [4, 4, 4], separator: '-', digitsOnly: true),
          ],
          validator: ExchekValidations.validateAadhaar,
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(PartnerAadharNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            final isValidAadhar =
                ExchekValidations.validateAadhaar(
                  state.partnerAadharNumberController.text.replaceAll("-", "").trim(),
                ) ==
                null;
            if (isValidAadhar) {
              context.read<BusinessAccountSetupBloc>().add(PartnerCaptchaSend());
            }
          },
        ),
      ],
    );
  }

  Widget _buildSendOTPButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.partnerCaptchaInputController]),
        builder: (context, child) {
          final isValidAadhar = state.partnerCaptchaInputController.text.isNotEmpty;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_send_otp,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isLoading: state.isPartnerOtpLoading,
            isDisabled: !isValidAadhar,
            borderRadius: 18.0,
            onPressed:
                isValidAadhar
                    ? () async {
                      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                      context.read<BusinessAccountSetupBloc>().add(
                        PartnerSendAadharOtp(
                          aadhar: state.partnerAadharNumberController.text.trim(),
                          captcha: state.partnerCaptchaInputController.text.trim(),
                          sessionId: sessionId,
                        ),
                      );
                    }
                    : null,
          );
        },
      ),
    );
  }

  Widget _buildOTPField(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Lang.of(context).lbl_OTP,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                fontWeight: FontWeight.w400,
                height: 1.22,
              ),
            ),
            buildSizedBoxH(5.0),
            CustomTextInputField(
              context: context,
              type: InputType.digits,
              controller: state.partnerAadharOtpController,
              textInputAction: TextInputAction.done,
              validator: ExchekValidations.validateOTP,
              suffixText: true,
              suffixIcon: ValueListenableBuilder(
                valueListenable: state.partnerAadharNumberController,
                builder: (context, _, __) {
                  return GestureDetector(
                    onTap:
                        state.isPartnerAadharOtpTimerRunning
                            ? null
                            : () async {
                              FocusManager.instance.primaryFocus?.unfocus();
                              final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                              BlocProvider.of<BusinessAccountSetupBloc>(context).add(
                                PartnerSendAadharOtp(
                                  aadhar: state.partnerAadharNumberController.text.trim(),
                                  captcha: state.partnerCaptchaInputController.text.trim(),
                                  sessionId: sessionId,
                                ),
                              );
                            },
                    child: Container(
                      color: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                      child: Text(
                        state.isPartnerAadharOtpTimerRunning
                            ? '${Lang.of(context).lbl_resend_otp_in} ${formatSecondsToMMSS(state.partnerAadharOtpRemainingTime)}sec'
                            : Lang.of(context).lbl_resend_OTP,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              state.isPartnerAadharOtpTimerRunning ||
                                      (state.partnerAadharNumberController.text.trim().length != 14)
                                  ? Theme.of(context).customColors.textdarkcolor?.withValues(alpha: 0.5)
                                  : Theme.of(context).customColors.greenColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
              maxLength: 6,
              contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly, NoPasteFormatter()],
              onFieldSubmitted: (value) {
                if (state.partnerAadharVerificationFormKey.currentState?.validate() ?? false) {
                  context.read<BusinessAccountSetupBloc>().add(
                    PartnerAadharNumbeVerified(
                      state.partnerAadharNumberController.text,
                      state.partnerAadharOtpController.text,
                    ),
                  );
                }
              },
              contextMenuBuilder: (BuildContext context, EditableTextState editableTextState) {
                return AdaptiveTextSelectionToolbar.buttonItems(
                  anchors: editableTextState.contextMenuAnchors,
                  buttonItems:
                      editableTextState.contextMenuButtonItems
                          .where((item) => item.type != ContextMenuButtonType.paste)
                          .toList(),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.partnerAadharOtpController, state.partnerAadharNumberController]),
        builder: (context, child) {
          final isDisable =
              state.partnerAadharOtpController.text.isEmpty || state.partnerAadharOtpController.text.isEmpty;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isPartnerAadharVerifiedLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisable,
            borderRadius: 18.0,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.partnerAadharVerificationFormKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          PartnerAadharNumbeVerified(
                            state.partnerAadharNumberController.text,
                            state.partnerAadharOtpController.text,
                          ),
                        );
                      }
                    },
          );
        },
      ),
    );
  }

  Widget _buildVerifyPartnerAadharNumber(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Partner Aadhaar Number",
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(
          value:
              ((state.partnerAadharNumber ?? "").contains("-"))
                  ? (state.partnerAadharNumber ?? '')
                  : GroupedInputFormatter.format(
                    input: state.partnerAadharNumber ?? '',
                    groupSizes: [4, 4, 4],
                    separator: '-',
                    digitsOnly: true,
                  ),
          showTrailingIcon: true,
        ),
      ],
    );
  }

  Widget _buildUploadAddharCard(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomFileUploadWidget(
              selectedFile: state.partnerFrontSideAdharFile,
              title: Lang.of(context).lbl_front_side_of_aadhar,
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(PartnerFrontSlideAadharCardUpload(fileData));
              },
            ),
            buildSizedBoxH(24.0),
            CustomFileUploadWidget(
              selectedFile: state.partnerBackSideAdharFile,
              title: Lang.of(context).lbl_back_side_of_aadhar,
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(PartnerBackSlideAadharCardUpload(fileData));
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isButtonEnabled = state.partnerFrontSideAdharFile != null && state.partnerBackSideAdharFile != null;

        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).save_and_next,
            borderRadius: 18.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 130 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isDisabled: !isButtonEnabled,
            isLoading: state.isPartnerAadharFileUploading,
            onPressed:
                isButtonEnabled
                    ? () {
                      context.read<BusinessAccountSetupBloc>().add(
                        PartnerAadharFileUploadSubmitted(
                          frontAadharFileData: state.partnerFrontSideAdharFile!,
                          backAadharFileData: state.partnerBackSideAdharFile!,
                        ),
                      );
                    }
                    : null,
          ),
        );
      },
    );
  }

  // Widget _buildTitleWidget(BuildContext context, String title) {
  //   return Text(
  //     title,
  //     style: Theme.of(context).textTheme.bodyMedium?.copyWith(
  //       fontSize: ResponsiveHelper.getFontSize(context, mobile: 14.0, tablet: 15.0, desktop: 16.0),
  //       fontWeight: FontWeight.w400,
  //       height: 1.22,
  //     ),
  //   );
  // }

  String formatSecondsToMMSS(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$secs';
  }

  Widget _buildVerifyAadharButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.partnerAadharNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validateAadhaar(state.partnerAadharNumberController.text.replaceAll("-", "").trim()) !=
              null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isPartnerCaptchaLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 18.0,
            onPressed:
                isDisabled
                    ? null
                    : () {
                      final isValidAadhar =
                          ExchekValidations.validateAadhaar(
                            state.partnerAadharNumberController.text.replaceAll("-", "").trim(),
                          ) ==
                          null;
                      if (isValidAadhar) {
                        context.read<BusinessAccountSetupBloc>().add(PartnerCaptchaSend());
                      }
                    },
          );
        },
      ),
    );
  }
}
