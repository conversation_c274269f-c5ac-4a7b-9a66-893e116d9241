import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/account_setup_widgets/beneficial_owner_pan_upload_dialog.dart';
import 'package:exchek/widgets/account_setup_widgets/business_representative_pan_upload_dialog.dart';
import 'package:exchek/widgets/account_setup_widgets/directors_pan_upload_dialog.dart';
import 'package:exchek/widgets/account_setup_widgets/other_director_kyc_dialog.dart';

class PanDetailView extends StatelessWidget {
  const PanDetailView({super.key});

  List<String> getUploadPanOptions(BuildContext context) => [
    // Lang.of(context).lbl_directors_PAN,
    // Lang.of(context).lbl_beneficial_owner_PAN,
    // Lang.of(context).lbl_business_representative_PAN,
    // "Director’s Aadhar",
    "Authorized Director KYC",
    "Other Director KYC",
    "Beneficial Owner's PAN",
  ];

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      listenWhen:
          (previous, current) =>
              previous.isBusinessPanCardSave != current.isBusinessPanCardSave ||
              previous.isDirectorPanCardSave != current.isDirectorPanCardSave ||
              previous.isBeneficialOwnerPanCardSave != current.isBeneficialOwnerPanCardSave ||
              previous.isbusinessRepresentativePanCardSave != current.isbusinessRepresentativePanCardSave,
      listener: (context, state) {
        // if (state.isBusinessPanCardSave == true && state.selectedUploadPanOption == getUploadPanOptions(context)[0]) {
        //   GoRouter.of(context).pop();
        // }

        if (state.isAuthorizedDirectorKycVerify == true &&
            state.selectedUploadPanOption == getUploadPanOptions(context)[0]) {
          GoRouter.of(context).pop();
        }

        if (state.isBeneficialOwnerPanCardSave == true &&
            state.selectedUploadPanOption == getUploadPanOptions(context)[2]) {
          GoRouter.of(context).pop();
        }

        // if (state.isbusinessRepresentativePanCardSave == true &&
        //     state.selectedUploadPanOption == getUploadPanOptions(context)[2]) {
        //   GoRouter.of(context).pop();
        // }
      },
      builder: (context, state) {
        return SingleChildScrollView(
          padding: EdgeInsets.only(bottom: ResponsiveHelper.isWebAndIsNotMobile(context) ? 50 : 20),
          child: Center(
            child: Container(
              constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxTileWidth(context)),
              padding: EdgeInsetsGeometry.symmetric(
                horizontal: ResponsiveHelper.isMobile(context) ? (kIsWeb ? 30.0 : 20) : 0.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildSizedBoxH(20.0),
                  _buildSelectionTitleAndDescription(
                    context: context,
                    title: "Director Identity Details",
                    description: "Enter government-issued identification details for two directors.",
                  ),
                  buildSizedBoxH(30.0),
                  Column(
                    children: List.generate(getUploadPanOptions(context).length, (index) {
                      return CustomTileWidget(
                        title: getUploadPanOptions(context)[index].toString(),
                        isSelected: state.selectedUploadPanOption == getUploadPanOptions(context)[index],
                        onTap: () {
                          final selectedOption = getUploadPanOptions(context)[index];

                          context.read<BusinessAccountSetupBloc>().add(
                            ChangeSelectedPanUploadOption(panUploadOption: getUploadPanOptions(context)[index]),
                          );
                          showUploadPanDialog(selectedOption, context);
                        },
                        isShowTrailing: true,
                        // isShowDone: isShowDoneIcon(state)[index],
                      );
                    }),
                  ),
                  buildSizedBoxH(14.0),
                  _buildNextButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  List<bool> isShowDoneIcon(BusinessAccountSetupState state) {
    return [
      state.isBusinessPanCardSave ?? false,
      state.isDirectorPanCardSave ?? false,
      state.isBeneficialOwnerPanCardSave ?? false,
      state.isbusinessRepresentativePanCardSave ?? false,
    ];
  }

  Widget _buildSelectionTitleAndDescription({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
            fontWeight: FontWeight.w500,
            letterSpacing: 0.32,
          ),
        ),
        buildSizedBoxH(14.0),
        Text(
          description,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            color: Theme.of(context).customColors.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isDisable =
            (state.isDirectorPanCardSave ?? false) &&
            (state.isBeneficialOwnerPanCardSave ?? false) &&
            (state.isbusinessRepresentativePanCardSave ?? false);

        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).save_and_next,
            borderRadius: 18.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 130 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isLoading: state.isPanDetailVerifyLoading ?? false,
            isDisabled: !isDisable,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            onPressed:
                isDisable
                    ? () {
                      context.read<BusinessAccountSetupBloc>().add(VerifyPanSubmitted());
                    }
                    : null,
          ),
        );
      },
    );
  }

  void showUploadPanDialog(String option, BuildContext context) {
    Widget dialog;

    switch (option) {
      // case "Business PAN":
      //   dialog = const BusinessPanUploadDialog();
      //   break;
      // case "Director's PAN":
      //   dialog = DirectorsPanUploadDialog();
      //   break;
      // case "Beneficial Owner's PAN":
      //   dialog = BeneficialOwnerPanUploadDialog();
      //   break;
      // case "Business Representative's PAN":
      //   dialog = BusinessRepresentativePanDetailDialog();
      //   break;
      // case "Director’s Aadhar":
      //   break Dialog();
      case "Authorized Director KYC":
        dialog = AuthorizedDirectorKycDialog();
        break;
      case "Other Director KYC":
        dialog = OtherDirectorKycDialog();
        break;
      case "Beneficial Owner's PAN":
        dialog = BeneficialOwnerPanUploadDialog();
        break;
      default:
        return;
    }

    showDialog(context: context, builder: (context) => dialog);
  }
}
