import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/viewmodels/account_setup_bloc/personal_account_setup_bloc/personal_account_setup_bloc.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';
import 'package:exchek/widgets/common_widget/app_toast_message.dart';

class PersonalPanDetailView extends StatelessWidget {
  const PersonalPanDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PersonalAccountSetupBloc, PersonalAccountSetupState>(
      listener: (context, state) {
        if (state.showPanNameOverwrittenPopup) {
          AppToast.show(message: 'Legal Fullname auto-updated as per PAN records.', type: ToastificationType.info);
          context.read<PersonalAccountSetupBloc>().add(PanNameOverwritePopupDismissed());
        }
      },
      builder: (context, state) {
        return ScrollConfiguration(
          behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
          child: SingleChildScrollView(
            padding: EdgeInsets.only(bottom: ResponsiveHelper.isWebAndIsNotMobile(context) ? 50 : 20),
            child: Center(
              child: Container(
                constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxTileWidth(context)),
                padding: EdgeInsetsGeometry.symmetric(
                  horizontal: ResponsiveHelper.isMobile(context) ? (kIsWeb ? 30.0 : 20) : 0.0,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSizedBoxH(20.0),
                    _buildSelectionTitleAndDescription(
                      context: context,
                      title: Lang.of(context).lbl_upload_PAN_details_for_KYC,
                      description: Lang.of(context).lbl_provide_identity_regulatory_regulatory,
                    ),
                    buildSizedBoxH(30.0),
                    Form(
                      key: state.panVerificationKey,
                      child: Column(
                        children: [
                          _buildPanCardNumberField(context, state),
                          if (!state.isPanDetailsVerified) buildSizedBoxH(20),
                          if (!state.isPanDetailsVerified) _buildVerifyPanButton(context, state),
                          buildSizedBoxH(24.0),
                          if (state.isPanDetailsVerified == true) ...[
                            _buildPersonalPanNameField(context, state),
                            buildSizedBoxH(24.0),
                            _buildUploadPanCard(context, state),
                            buildSizedBoxH(30.0),
                            _buildNextButton(),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSelectionTitleAndDescription({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 28, tablet: 30, desktop: 32),
            fontWeight: FontWeight.w500,
            letterSpacing: 0.32,
          ),
        ),
        buildSizedBoxH(14.0),
        Text(
          description,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            color: Theme.of(context).customColors.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalPanNameField(BuildContext context, PersonalAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_name_on_PAN,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(value: state.panNameController.text, showTrailingIcon: true),
        // CustomTextInputField(
        //   context: context,
        //   type: InputType.text,
        //   controller: state.panNameController,
        //   textInputAction: TextInputAction.next,
        //   contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
        //   validator: ExchekValidations.validateRequired,
        // ),
      ],
    );
  }

  Widget _buildPanCardNumberField(BuildContext context, PersonalAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_pan_number,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          controller: state.panNumberController,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          validator: ExchekValidations.validatePAN,
          maxLength: 10,
          onChanged: (value) {
            context.read<PersonalAccountSetupBloc>().add(PersonalPanNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            // Show validation error if any
            state.panVerificationKey.currentState?.validate();

            // If valid, proceed
            if (ExchekValidations.validatePAN(value) == null) {
              context.read<PersonalAccountSetupBloc>().add(GetPanDetails(value));
            }
          },
          autovalidateMode: AutovalidateMode.onUserInteraction,
          inputFormatters: [UpperCaseTextFormatter()],
        ),
      ],
    );
  }

  Widget _buildUploadPanCard(BuildContext context, PersonalAccountSetupState state) {
    return CustomFileUploadWidget(
      title: Lang.of(context).lbl_upload_pan_card,
      selectedFile: state.panFileData,
      onFileSelected: (fileData) {
        context.read<PersonalAccountSetupBloc>().add(PersonalUploadPanCard(fileData));
      },
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<PersonalAccountSetupBloc, PersonalAccountSetupState>(
      builder: (context, state) {
        final isButtonEnabled =
            state.panFileData != null &&
            state.panNameController.text.isNotEmpty &&
            state.panNumberController.text.isNotEmpty;

        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).save_and_next,
            borderRadius: 18.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 130 : double.maxFinite,
            isLoading: state.isPanVerifyingLoading ?? false,
            isDisabled: !isButtonEnabled,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            onPressed:
                isButtonEnabled
                    ? () {
                      if (state.panVerificationKey.currentState?.validate() ?? false) {
                        context.read<PersonalAccountSetupBloc>().add(
                          PersonalPanVerificationSubmitted(
                            fileData: state.panFileData,
                            panName: state.panNameController.text,
                            panNumber: state.panNumberController.text,
                          ),
                        );
                      }
                    }
                    : null,
          ),
        );
      },
    );
  }

  Widget _buildVerifyPanButton(BuildContext context, PersonalAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.panNumberController]),
        builder: (context, _) {
          bool isDisabled = ExchekValidations.validatePAN(state.panNumberController.text) != null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isPanDetailsLoading,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 18.0,
            onPressed: () {
              // Show validation error if any
              state.panVerificationKey.currentState?.validate();

              // If valid, proceed
              if (ExchekValidations.validatePAN(state.panNumberController.text) == null) {
                context.read<PersonalAccountSetupBloc>().add(GetPanDetails(state.panNumberController.text));
              }
            },
            // isDisabled
            //     ? null
            //     : () {
            //       final isValidAadhar =
            //           ExchekValidations.validateAadhaar(
            //             state.aadharNumberController.text.replaceAll("-", "").trim(),
            //           ) ==
            //           null;
            //       if (isValidAadhar) {
            //         context.read<PersonalAccountSetupBloc>().add(CaptchaSend());
            //       }
            //     },
          );
        },
      ),
    );
  }
}
