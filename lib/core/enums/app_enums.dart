enum RequestType { GET, POST, PUT, DELETE, PATCH, MULTIPART_POST }

enum Flavor { prod, stage, dev }

enum LogMode { debug, live }

enum AccountType { personal, business }

enum BusinessMainActivity { exportGoods, exportService, exportBoth, others }

enum BusinessAccountSetupSteps { businessEntity, businessInformation, transactionAndPaymentPreferences, setPassword }

enum KycVerificationSteps {
  aadharVerfication,
  panVerification,
  registeredOfficeAddress,
  annualTurnoverDeclaration,
  iecVerification,
  companyIncorporationVerification,
  bankAccountLinking,
  contactInformation,
}

enum PersonalEKycVerificationSteps {
  identityVerification,
  panDetails,
  residentialAddress,
  annualTurnoverDeclaration,
  iecVerification,
  bankAccountLinking,
  // selfie,
}

enum IDVerificationDocType { aadharCard, passport, drivingLicense, voterID }

enum PersonalAccountSetupSteps { personalEntity, personalInformation, personalTransactions, setPassword }

enum LoginType { phone, email }

enum InputType {
  name,
  text,
  email,
  password,
  confirmPassword,
  newPassword,
  phoneNumber,
  digits,
  decimalDigits,
  multiline,
}

enum ImageType { svg, png, network, file, lottie, unknown }
