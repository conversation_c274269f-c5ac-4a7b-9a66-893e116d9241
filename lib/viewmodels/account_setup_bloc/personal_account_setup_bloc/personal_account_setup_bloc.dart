// ignore_for_file: invalid_use_of_visible_for_testing_member

import 'dart:convert';

import 'package:camera/camera.dart';
import 'package:country_picker/country_picker.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/models/personal_user_models/aadhar_otp_model.dart';
import 'package:exchek/models/personal_user_models/aadhar_verify_otp_model.dart';
import 'package:exchek/models/personal_user_models/captcha_model.dart';
import 'package:exchek/models/personal_user_models/get_city_and_state_model.dart';
import 'package:exchek/models/personal_user_models/get_pan_detail_model.dart';
import 'package:exchek/models/personal_user_models/recaptcha_model.dart';
import 'package:exchek/repository/personal_user_kyc_repository.dart';
import 'package:exchek/views/account_setup_view/personal_account_setup_view/personal_transaction_payment_reference_view.dart';
import 'package:exchek/widgets/common_widget/app_toast_message.dart';
import 'package:exchek/models/personal_user_models/get_currency_model.dart';
import 'package:cron/cron.dart';
part 'personal_account_setup_event.dart';
part 'personal_account_setup_state.dart';

extension IDVerificationDocTypeExtension on IDVerificationDocType {
  String get displayName {
    switch (this) {
      case IDVerificationDocType.aadharCard:
        return Lang.current.lbl_aadhar_card;
      case IDVerificationDocType.drivingLicense:
        return Lang.current.lbl_driving_license;
      case IDVerificationDocType.voterID:
        return Lang.current.lbl_voter_id;
      case IDVerificationDocType.passport:
        return Lang.current.lbl_passport;
    }
  }
}

class PersonalAccountSetupBloc extends Bloc<PersonalAccountSetupEvent, PersonalAccountSetupState> {
  static const int initialTime = 120;
  Timer? _timer;

  Timer? _aadhartimer;

  Timer? _resendTimer;

  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  Uint8List? _capturedImageBytes;
  final AuthRepository _authRepository;
  final PersonalUserKycRepository _personalUserKycRepository;
  Cron? _cron;

  // Static GlobalKeys to prevent conflicts
  static final GlobalKey<FormState> _aadharVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _drivingVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _voterVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _passportVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _panVerificationKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _registerAddressFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _annualTurnoverFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _personalBankAccountVerificationFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _personalInfoKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _sePasswordFormKey = GlobalKey<FormState>();
  static final GlobalKey<FormState> _iceVerificationKey = GlobalKey<FormState>();

  PersonalAccountSetupBloc({
    required AuthRepository authRepository,
    required PersonalUserKycRepository personalUserKycRepository,
  }) : _authRepository = authRepository,
       _personalUserKycRepository = personalUserKycRepository,
       super(
         PersonalAccountSetupState(
           scrollController: ScrollController(),
           professionOtherController: TextEditingController(),
           productServiceDescriptionController: TextEditingController(),
           passwordController: TextEditingController(),
           confirmPasswordController: TextEditingController(),
           currencyList: [],
           selectedCurrencies: [],
           currentKycVerificationStep: PersonalEKycVerificationSteps.identityVerification,
           aadharNumberController: TextEditingController(),
           aadharOtpController: TextEditingController(),
           aadharVerificationFormKey: _aadharVerificationFormKey,
           drivingVerificationFormKey: _drivingVerificationFormKey,
           drivingLicenceController: TextEditingController(),
           voterVerificationFormKey: _voterVerificationFormKey,
           voterIdNumberController: TextEditingController(),
           passportVerificationFormKey: _passportVerificationFormKey,
           passportNumberController: TextEditingController(),
           panVerificationKey: _panVerificationKey,
           panNameController: TextEditingController(),
           panNumberController: TextEditingController(),
           registerAddressFormKey: _registerAddressFormKey,
           pinCodeController: TextEditingController(),
           stateNameController: TextEditingController(),
           cityNameController: TextEditingController(),
           address1NameController: TextEditingController(),
           address2NameController: TextEditingController(),
           annualTurnoverFormKey: _annualTurnoverFormKey,
           turnOverController: TextEditingController(),
           gstNumberController: TextEditingController(),
           personalBankAccountVerificationFormKey: _personalBankAccountVerificationFormKey,
           bankAccountNumberController: TextEditingController(),
           reEnterbankAccountNumberController: TextEditingController(),
           ifscCodeController: TextEditingController(),
           fullNameController: TextEditingController(),
           websiteController: TextEditingController(),
           mobileController: TextEditingController(),
           otpController: TextEditingController(),
           personalInfoKey: _personalInfoKey,
           sePasswordFormKey: _sePasswordFormKey,
           selectedCountry: Country(
             phoneCode: '91',
             countryCode: 'IN',
             e164Sc: 0,
             geographic: true,
             level: 1,
             name: 'India',
             example: '**********',
             displayName: 'India',
             displayNameNoCountryCode: 'India',
             e164Key: '',
           ),
           captchaInputController: TextEditingController(),
           familyAndFriendsDescriptionController: TextEditingController(),
           iceVerificationKey: _iceVerificationKey,
           iceNumberController: TextEditingController(),
         ),
       ) {
    on<PersonalInfoStepChanged>(_onPersonalInfoStepChanged);
    on<NextStep>(_onNextStep);
    on<PreviousStepEvent>(_onPreviousStep);
    on<ChangePurpose>(_onChangePurpose);
    on<ChangeProfession>(_onChangeProfession);
    on<UpdatePersonalDetails>(_onUpdatePersonalDetails);
    on<PersonalPasswordSubmitted>(_onPersonalPasswordSubmitted);
    on<PersonalChangeEstimatedMonthlyTransaction>(_onPersonalChangeEstimatedMonthlyTransaction);
    on<PersonalToggleCurrencySelection>(_onPersonalToggleCurrencySelection);
    on<PersonalTransactionDetailSubmitted>(_onPersonalTransactionDetailSubmitted);
    on<PersonalKycStepChange>(_onPersonalKycStepChange);
    on<PersonalUpdateIdVerificationDocType>(_onPersonalUpdateIdVerificationDocType);
    on<PersonalSendAadharOtp>(_onPersonalSendAadharOtp);
    on<ChangeOtpSentStatus>(_onChangeOtpSentStatus);
    on<AadharSendOtpPressed>(_onAadharSendOtpPressed);
    on<AadharOtpTimerTicked>(_onAadharOtpTimerTicked);
    on<PersonalAadharNumbeVerified>(_onPersonalAadharNumbeVerified);
    on<PersonalDrivingLicenceVerified>(_onPersonalDrivingLicenceVerified);
    on<PersonalFrontSlideAadharCardUpload>(_onPersonalFrontSlideAadharCardUpload);
    on<PersonalBackSlideAadharCardUpload>(_onPersonalBackSlideAadharCardUpload);
    on<PersonalAadharFileUploadSubmitted>(_onPersonalAadharFileUploadSubmitted);
    on<PersonalFrontSlideDrivingLicenceUpload>(_onPersonalFrontSlideDrivingLicenceUpload);
    on<PersonalDrivingFileUploadSubmitted>(_onPersonalDrivingFileUploadSubmitted);
    on<PersonalVoterIdVerified>(_onPersonalVoterIdVerified);
    on<PersonalVoterIdFileUploadSubmitted>(_onPersonalVoterIdFileUploadSubmitted);
    on<PersonalVoterIdFileUpload>(_onPersonalVoterIdFileUpload);
    on<PersonalPassportVerified>(_onPersonalPassportVerified);
    on<PersonalPassportFileUploadSubmitted>(_onPersonalPassportFileUploadSubmitted);
    on<PersonalPassportFileUpload>(_onPersonalPassportFileUpload);
    on<PersonalUploadPanCard>(_onPersonalUploadPanCard);
    on<PersonalPanVerificationSubmitted>(_onPersonalPanVerificationSubmitted);
    on<PersonalUpdateSelectedCountry>(_onPersonalUpdateSelectedCountry);
    on<PersonalUpdateAddressVerificationDocType>(_onPersonalUpdateAddressVerificationDocType);
    on<PersonalUploadAddressVerificationFile>(_onPersonalUploadAddressVerificationFile);
    on<PersonalUploadBackAddressVerificationFile>(_onPersonalUploadBackAddressVerificationFile);
    on<PersonalRegisterAddressSubmitted>(_onPersonalRegisterAddressSubmitted);
    on<PersonalUploadGstCertificateFile>(_onPersonalUploadGstCertificateFile);
    on<PersonalAnnualTurnOverVerificationSubmitted>(_onPersonalAnnualTurnOverVerificationSubmitted);
    on<PersonalBankAccountNumberVerify>(_onPersonalBankAccountNumberVerify);
    on<PersonalUpdateBankAccountVerificationDocType>(_onPersonalUpdateBankAccountVerificationDocType);
    on<PersonalUploadBankAccountVerificationFile>(_onPersonalUploadBankAccountVerificationFile);
    on<PersonalBankAccountDetailSubmitted>(_onPersonalBankAccountDetailSubmitted);
    //TODO: SELFIE STEP EVENT
    // on<InitializeSelfieEvent>(_onInitializeSelfie);
    // on<CaptureImageEvent>(_onCaptureImage);
    // on<RetakeImageEvent>(_onRetakeImage);
    // on<SubmitImageEvent>(_onSubmitImage);
    // on<RequestPermissionEvent>(_onRequestPermission);
    // on<DisposeSelfieEvent>(_onDisposeSelfie);
    on<PersonalScrollToPosition>(_onScrollToSection);
    on<SendOTP>(_onSendOTP);
    on<UpdateOTPError>(_onUpdateOTPError);
    on<ConfirmAndContinue>(_onConfirmAndContinue);
    on<UpdateResendTimerState>(_onUpdateResendTimerState);
    on<TogglePasswordVisibility>(_onTogglePasswordVisibility);
    on<ToggleConfirmPasswordVisibility>(_onToggleConfirmPasswordVisibility);
    on<PasswordChanged>(_onPasswordChanged);
    on<ConfirmPasswordChanged>(_onConfirmPasswordChanged);
    on<PersonalResetData>(_onPersonalResetData);
    on<PersonalResetSignupSuccess>(_onPersonalResetSignupSuccess);
    on<GetPersonalCurrencyOptions>(_onGetPersonalCurrencyOptions);
    on<CaptchaSend>(_onCaptchaSend);
    on<ReCaptchaSend>(_onReCaptchaSend);
    on<PersonalClearIdentityVerificationFields>(_onClearIdentityVerificationFields);
    on<ResidenceAddressSameAsAadhar>(_onResidenceAddressSameAsAadhar);
    on<GetPanDetails>(_onGetPanDetails);
    on<PersonalPanNumberChanged>(_onPersonalPanNumberChanged);
    on<GetCityAndState>(_onGetCityAndState);
    on<ChangeAgreeToAddressSameAsAadhar>(_onChangeAgreeToAddressSameAsAadhar);
    on<PersonalChangeAnnualTurnover>(_onPersonalChangeAnnualTurnover);
    on<PersonalAppBarCollapseChanged>(_onPersonalAppBarCollapseChanged);
    on<PersonalEkycAppBarCollapseChanged>(_onPersonalEkycAppBarCollapseChanged);
    on<PanNameOverwritePopupDismissed>(_onPanNameOverwritePopupDismissed);
    on<PersonalGSTVerification>(_onPersonalGSTVerification);
    on<PersonalUploadICECertificate>(_onUploadICECertificate);
    on<PersonalICEVerificationSubmitted>(_onICEVerificationSubmitted);
    on<PersonalAadharNumberChanged>(_onPersonalAadharNumberChanged);
    on<LoadPersonalKycFromLocal>(_onLoadPersonalKycFromLocal);
    on<PersonalChangeShowDescription>(_onPersonalChangeShowDescription);

    // Start cron job every 9 minutes to refresh only file data
    _cron = Cron();
    _cron!.schedule(Schedule.parse('*/10 * * * *'), () async {
      await _refreshKycFileData();
    });
  }

  void _onPersonalInfoStepChanged(PersonalInfoStepChanged event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(currentStep: event.step));
  }

  void _onPanNameOverwritePopupDismissed(
    PanNameOverwritePopupDismissed event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    emit(state.copyWith(showPanNameOverwrittenPopup: false));
  }

  void _onNextStep(NextStep event, Emitter<PersonalAccountSetupState> emit) {
    final currentIndex = PersonalAccountSetupSteps.values.indexOf(state.currentStep);
    if (currentIndex < PersonalAccountSetupSteps.values.length - 1) {
      emit(state.copyWith(currentStep: PersonalAccountSetupSteps.values[currentIndex + 1]));
    }
  }

  void _onPreviousStep(PreviousStepEvent event, Emitter<PersonalAccountSetupState> emit) {
    final currentIndex = PersonalAccountSetupSteps.values.indexOf(state.currentStep);
    if (currentIndex > 0) {
      emit(state.copyWith(currentStep: PersonalAccountSetupSteps.values[currentIndex - 1]));
    }
  }

  void _onChangePurpose(ChangePurpose event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(selectedPurpose: event.purpose));
  }

  void _onChangeProfession(ChangeProfession event, Emitter<PersonalAccountSetupState> emit) {
    final currentSelected = List<String>.from(state.selectedProfession ?? []);
    if (event.profession == 'Others') {
      emit(state.copyWith(selectedProfession: ['Others']));
    } else {
      if (currentSelected.contains('Others')) {
        currentSelected.remove('Others');
      }
      if (currentSelected.contains(event.profession)) {
        currentSelected.remove(event.profession);
      } else {
        currentSelected.add(event.profession);
      }
      emit(state.copyWith(selectedProfession: currentSelected));
      if ((state.selectedProfession ?? []).isEmpty) {
        add(PersonalChangeShowDescription(false));
      }
    }
  }

  void _onPersonalChangeAnnualTurnover(
    PersonalChangeAnnualTurnover event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    try {
      bool isMandatory = !(event.selectedIndex.contains("Less than"));
      emit(
        state.copyWith(
          selectedAnnualTurnover: event.selectedIndex,
          isGstCertificateMandatory: isMandatory,
          isGSTNumberVerify: false,
        ),
      );
    } catch (e) {
      Logger.error('Error saving business entity type: $e');
    }
  }

  void _onUpdatePersonalDetails(UpdatePersonalDetails event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(fullName: event.fullName, website: event.website, phoneNumber: event.phoneNumber));
  }

  Future<void> _onPersonalPasswordSubmitted(
    PersonalPasswordSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, navigateNext: false));
    try {
      final emailIdtoken = await Prefobj.preferences.get(Prefkeys.verifyemailToken) ?? '';
      final tosacceptance = await UserAgentHelper.getPlatformMetaInfo();

      // Convert CurrencyModel list to String list for API
      final List<String> multicurrencyStrings =
          (state.selectedCurrencies ?? [])
              .map((currency) => '${currency.currencySymbol} ${currency.currencyName}')
              .toList();
      List<String> professionList = [];
      if ((state.selectedProfession?.isNotEmpty ?? false)) {
        if (state.selectedProfession!.length == 1 && state.selectedProfession!.first == 'Others') {
          if (state.productServiceDescriptionController.text.trim().isNotEmpty) {
            professionList = [state.productServiceDescriptionController.text.trim()];
          } else {
            professionList = state.selectedProfession ?? [];
          }
        } else {
          professionList = state.selectedProfession ?? [];
        }
      }

      final response = await _authRepository.registerPersonalUser(
        email: emailIdtoken,
        estimatedMonthlyVolume: state.selectedEstimatedMonthlyTransaction ?? '',
        multicurrency: multicurrencyStrings,
        mobileNumber: state.phoneNumber ?? '',
        receivingreason: state.selectedPurpose ?? '',
        profession: professionList,
        productDescription: state.productServiceDescriptionController.text,
        legalFullName: state.fullName ?? '',
        password: state.passwordController.text,
        tosacceptance: tosacceptance,
        usertype: 'personal',
        website: state.website ?? '',
      );
      if (response.success == true) {
        await Prefobj.preferences.put(Prefkeys.authToken, 'exchek@123');
        await Prefobj.preferences.delete(Prefkeys.verifyemailToken);
        emit(state.copyWith(isLoading: false, isSignupSuccess: true));
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
      Logger.error('Error during personal user registration: $e');
    }
  }

  void _onPersonalResetSignupSuccess(PersonalResetSignupSuccess event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(isSignupSuccess: false));
  }

  void _onPersonalChangeEstimatedMonthlyTransaction(
    PersonalChangeEstimatedMonthlyTransaction event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    emit(state.copyWith(selectedEstimatedMonthlyTransaction: event.transactionAmount));
  }

  void _onPersonalToggleCurrencySelection(
    PersonalToggleCurrencySelection event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    final currentSelected = List<CurrencyModel>.from(state.selectedCurrencies ?? []);
    final existingIndex = currentSelected.indexWhere(
      (currency) => currency.currencySymbol == event.currency.currencySymbol,
    );

    if (existingIndex != -1) {
      currentSelected.removeAt(existingIndex);
    } else {
      currentSelected.add(event.currency);
    }

    emit(state.copyWith(selectedCurrencies: currentSelected));
  }

  Future<void> _onPersonalTransactionDetailSubmitted(
    PersonalTransactionDetailSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isTransactionDetailLoading: true));

    try {
      await Future.delayed(const Duration(seconds: 2));
      add(const NextStep());
      emit(state.copyWith(isTransactionDetailLoading: false));
    } catch (e) {
      emit(state.copyWith(isTransactionDetailLoading: false));
      debugPrint('Error submitting transaction details: $e');
    }
  }

  void _onPersonalKycStepChange(PersonalKycStepChange event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(currentKycVerificationStep: event.stepIndex));
  }

  void _onPersonalUpdateIdVerificationDocType(
    PersonalUpdateIdVerificationDocType event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    add(PersonalClearIdentityVerificationFields());
    emit(state.copyWith(selectedIDVerificationDocType: event.docType, isIdVerified: false, isDrivingIdVerified: false));
  }

  void _onPersonalSendAadharOtp(PersonalSendAadharOtp event, Emitter<PersonalAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isOtpSent: false, isOtpLoading: true));
      AadharOTPSendModel response = await _personalUserKycRepository.generateAadharOTP(
        aadhaarNumber: event.aadhar.replaceAll("-", ""),
        captcha: event.captcha,
        sessionId: event.sessionId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isOtpSent: true, isOtpLoading: false));
        add(AadharSendOtpPressed());
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isOtpLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isOtpSent: false, isOtpLoading: false));
    }
  }

  void _onCaptchaSend(CaptchaSend event, Emitter<PersonalAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isCaptchaLoading: true, isCaptchaSend: false));
      final CaptchaModel response = await _personalUserKycRepository.generateCaptcha();
      if (response.code == 200) {
        emit(state.copyWith(isCaptchaSend: true, isCaptchaLoading: false, captchaImage: response.data?.captcha ?? ''));
        await Prefobj.preferences.put(Prefkeys.sessionId, response.data?.sessionId ?? '');
      } else {
        emit(state.copyWith(isCaptchaLoading: false, isCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isCaptchaLoading: false, isCaptchaSend: false));
      Logger.error('Error :: $e');
    }
  }

  void _onReCaptchaSend(ReCaptchaSend event, Emitter<PersonalAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isCaptchaLoading: true, isCaptchaSend: false));
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';

      final RecaptchaModel response = await _personalUserKycRepository.reGenerateCaptcha(sessionId: sessionId);
      if (response.code == 200) {
        emit(state.copyWith(isCaptchaSend: true, isCaptchaLoading: false, captchaImage: response.data?.captcha ?? ''));
        state.captchaInputController.clear();
      } else {
        emit(state.copyWith(isCaptchaLoading: false, isCaptchaSend: false));
      }
    } catch (e) {
      emit(state.copyWith(isCaptchaLoading: false, isCaptchaSend: false));
      Logger.error(e.toString());
    }
  }

  void _onChangeOtpSentStatus(ChangeOtpSentStatus event, Emitter<PersonalAccountSetupState> emit) async {
    emit(state.copyWith(isOtpSent: event.isOtpSent));
  }

  void _onAadharSendOtpPressed(AadharSendOtpPressed event, Emitter<PersonalAccountSetupState> emit) {
    _aadhartimer?.cancel();
    emit(state.copyWith(isAadharOtpTimerRunning: true, aadharOtpRemainingTime: initialTime));
    _aadhartimer = Timer.periodic(Duration(seconds: 1), (timer) {
      final newTime = state.aadharOtpRemainingTime - 1;
      if (newTime <= 0) {
        timer.cancel();
        add(AadharOtpTimerTicked(0));
      } else {
        add(AadharOtpTimerTicked(newTime));
      }
    });
  }

  void _onAadharOtpTimerTicked(AadharOtpTimerTicked event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(aadharOtpRemainingTime: event.remainingTime, isAadharOtpTimerRunning: event.remainingTime > 0));
  }

  void _onPersonalAadharNumbeVerified(
    PersonalAadharNumbeVerified event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIdVerifiedLoading: true, isAadharOTPInvalidate: null));
    try {
      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';

      final AadharOTPVerifyModel response = await _personalUserKycRepository.validateAadharOtp(
        faker: false,
        otp: event.otp,
        sessionId: sessionId,
        userId: userId,
      );
      if (response.code == 200) {
        emit(state.copyWith(isIdVerifiedLoading: false, isIdVerified: true, aadharNumber: event.aadharNumber));
        _aadhartimer?.cancel();
      } else {
        emit(state.copyWith(isIdVerifiedLoading: false, isAadharOTPInvalidate: response.message.toString()));
      }
    } catch (e) {
      emit(state.copyWith(isIdVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalDrivingLicenceVerified(
    PersonalDrivingLicenceVerified event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIdVerifiedLoading: true));
    try {
      await Future.delayed(Duration(milliseconds: 300));
      emit(state.copyWith(isIdVerifiedLoading: false, isIdVerified: true, drivingLicenseNumber: event.drivingLicence));
    } catch (e) {
      emit(state.copyWith(isIdVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalFrontSlideAadharCardUpload(
    PersonalFrontSlideAadharCardUpload event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    if (event.fileData == null) {
      emit(state.copyWith(frontSideAdharFile: null));
    } else {
      emit(state.copyWith(frontSideAdharFile: event.fileData));
    }
  }

  void _onPersonalBackSlideAadharCardUpload(
    PersonalBackSlideAadharCardUpload event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    if (event.fileData == null) {
      emit(state.copyWith(backSideAdharFile: null));
    } else {
      emit(state.copyWith(backSideAdharFile: event.fileData));
    }
  }

  void _onPersonalFrontSlideDrivingLicenceUpload(
    PersonalFrontSlideDrivingLicenceUpload event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    emit(state.copyWith(drivingLicenceFrontSideFile: event.fileData));
  }

  void _onPersonalAadharFileUploadSubmitted(
    PersonalAadharFileUploadSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIdFileSubmittedLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadPersonalKyc(
        userID: userId ?? '',
        documentType: 'Aadhaar',
        documentNumber: state.aadharNumber?.replaceAll("-", "") ?? '',
        documentFrontImage: event.frontAadharFileData!,
        documentBackImage: event.backAadharFileData,
        isAddharCard: true,
        nameOnPan: state.panNameController.text,
        userType: 'personal',
      );
      if (response.success == true) {
        final index = state.currentKycVerificationStep.index;
        if (index < KycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
        }
        emit(state.copyWith(isIdFileSubmittedLoading: false));
      } else {
        emit(state.copyWith(isIdFileSubmittedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isIdFileSubmittedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalDrivingFileUploadSubmitted(
    PersonalDrivingFileUploadSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIdFileSubmittedLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadPersonalKyc(
        userID: userId ?? '',
        documentType: 'DrivingLicense',
        documentNumber: state.drivingLicenceController.text.trim(),
        documentFrontImage: event.frontDrivingLicenceFileData!,
        isAddharCard: false,
        nameOnPan: state.panNameController.text,
        userType: 'personal',
      );
      if (response.success == true) {
        final index = state.currentKycVerificationStep.index;
        if (index < KycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
        }
        emit(state.copyWith(isIdFileSubmittedLoading: false));
      } else {
        emit(state.copyWith(isIdFileSubmittedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isIdFileSubmittedLoading: false));
    }
  }

  void _onPersonalVoterIdVerified(PersonalVoterIdVerified event, Emitter<PersonalAccountSetupState> emit) async {
    emit(state.copyWith(isIdVerifiedLoading: true));
    try {
      await Future.delayed(Duration(milliseconds: 300));
      emit(state.copyWith(isIdVerifiedLoading: false, isIdVerified: true, voterIDNumber: event.voterId));
    } catch (e) {
      emit(state.copyWith(isIdVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalVoterIdFileUploadSubmitted(
    PersonalVoterIdFileUploadSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIdFileSubmittedLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadPersonalKyc(
        userID: userId ?? '',
        documentType: 'VoterID',
        documentNumber: state.voterIdNumberController.text.trim(),
        documentFrontImage: event.voterIdFileData!,
        isAddharCard: false,
        nameOnPan: state.panNameController.text,
        userType: 'personal',
      );
      if (response.success == true) {
        final index = state.currentKycVerificationStep.index;
        if (index < KycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
        }
        emit(state.copyWith(isIdFileSubmittedLoading: false));
      } else {
        emit(state.copyWith(isIdVerifiedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isIdFileSubmittedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalVoterIdFileUpload(PersonalVoterIdFileUpload event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(voterIdFileData: event.fileData));
  }

  void _onPersonalPassportVerified(PersonalPassportVerified event, Emitter<PersonalAccountSetupState> emit) async {
    emit(state.copyWith(isIdVerifiedLoading: true));
    try {
      await Future.delayed(Duration(milliseconds: 300));
      emit(state.copyWith(isIdVerifiedLoading: false, isIdVerified: true, passporteNumber: event.passportNumber));
    } catch (e) {
      emit(state.copyWith(isIdVerifiedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalPassportFileUploadSubmitted(
    PersonalPassportFileUploadSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIdFileSubmittedLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);
    try {
      final response = await _personalUserKycRepository.uploadPersonalKyc(
        userID: userId ?? '',
        documentType: 'Passport',
        documentNumber: state.passportNumberController.text.trim(),
        documentFrontImage: event.passportFileData!,
        isAddharCard: false,
        nameOnPan: state.panNameController.text,
        userType: 'personal',
      );
      if (response.success == true) {
        final index = state.currentKycVerificationStep.index;
        if (index < KycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
        }
        emit(state.copyWith(isIdFileSubmittedLoading: false));
      } else {
        emit(state.copyWith(isIdVerifiedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isIdFileSubmittedLoading: false));
      Logger.error(e.toString());
    }
  }

  void _onPersonalPassportFileUpload(PersonalPassportFileUpload event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(passportFileData: event.fileData));
  }

  void _onPersonalUploadPanCard(PersonalUploadPanCard event, Emitter<PersonalAccountSetupState> emit) async {
    emit(state.copyWith(panFileData: event.fileData));
  }

  void _onPersonalPanVerificationSubmitted(
    PersonalPanVerificationSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    if (state.panVerificationKey.currentState?.validate() ?? false) {
      emit(state.copyWith(isPanVerifyingLoading: true));

      final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

      try {
        final response = await _personalUserKycRepository.uploadPersonalKyc(
          userID: userId ?? '',
          documentType: 'Pan',
          documentNumber: state.panNumberController.text.trim(),
          documentFrontImage: event.fileData!,
          isAddharCard: false,
          nameOnPan: state.panNameController.text,
          userType: 'personal',
        );
        if (response.success == true) {
          final index = state.currentKycVerificationStep.index;
          if (index < KycVerificationSteps.values.length - 1) {
            add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
          }
          emit(state.copyWith(isPanVerifyingLoading: false));
        } else {
          emit(state.copyWith(isPanVerifyingLoading: false));
        }
      } catch (e) {
        emit(state.copyWith(isPanVerifyingLoading: false));
      }
    }
  }

  void _onPersonalUpdateSelectedCountry(
    PersonalUpdateSelectedCountry event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(selectedCountry: event.country));
  }

  void _onPersonalUpdateAddressVerificationDocType(
    PersonalUpdateAddressVerificationDocType event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(selectedAddressVerificationDocType: event.docType));
  }

  void _onPersonalUploadAddressVerificationFile(
    PersonalUploadAddressVerificationFile event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(addressVerificationFile: event.fileData));
  }

  void _onPersonalUploadBackAddressVerificationFile(
    PersonalUploadBackAddressVerificationFile event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(backAddressVerificationFile: event.fileData));
  }

  void _onPersonalRegisterAddressSubmitted(
    PersonalRegisterAddressSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isAddressVerificationLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadResidentialAddressDetails(
        userID: userId ?? '',
        userType: 'personal',
        documentType: event.docType,
        addressLine1: state.address1NameController.text.trim(),
        addressLine2: state.address2NameController.text.trim(),
        city: state.cityNameController.text.trim(),
        isAddharCard: event.isAddharCard,
        country: state.selectedCountry?.name ?? '',
        pinCode: state.pinCodeController.text.trim(),
        state: state.stateNameController.text.trim(),
        documentFrontImage: event.addressValidateFileData,
        documentBackImage: event.isAddharCard ? event.backValiateFileData : null,
        aadhaarUsedAsIdentity: state.isResidenceAddressSameAsAadhar == 0 ? 'yes' : 'no',
      );
      if (response.success == true) {
        final index = state.currentKycVerificationStep.index;
        if (index < KycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
        }
        emit(state.copyWith(isAddressVerificationLoading: false));
      } else {
        AppToast.show(message: response.message ?? '', type: ToastificationType.error);
        emit(state.copyWith(isAddressVerificationLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isAddressVerificationLoading: false));
    }
  }

  void _onPersonalUploadGstCertificateFile(
    PersonalUploadGstCertificateFile event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(gstCertificateFile: event.fileData));
  }

  void _onPersonalGSTVerification(PersonalGSTVerification event, Emitter<PersonalAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isGstVerificationLoading: true));

      final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

      final response = await _personalUserKycRepository.getGSTDetails(
        userID: userId ?? '',
        estimatedAnnualIncome: event.turnover,
        gstNumber: event.gstNumber,
      );
      if (response.success == true) {
        emit(
          state.copyWith(
            isGstVerificationLoading: false,
            gstLegalName: response.data?.legalName ?? '',
            isGSTNumberVerify: true,
          ),
        );
      } else {
        emit(state.copyWith(isGstVerificationLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isGstVerificationLoading: false));
    }
  }

  void _onPersonalAnnualTurnOverVerificationSubmitted(
    PersonalAnnualTurnOverVerificationSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isGstVerificationLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadGSTDocument(
        userID: userId ?? '',
        gstNumber: event.gstNumber,
        userType: 'personal',
        gstCertificate: event.gstCertificate,
      );
      if (response.success == true) {
        final currentIndex = state.currentKycVerificationStep.index;
        if (currentIndex < PersonalEKycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[currentIndex + 1]));
        }
        emit(state.copyWith(isGstVerificationLoading: false));
      } else {
        emit(state.copyWith(isGstVerificationLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isGstVerificationLoading: false));
    }
  }

  void _onPersonalBankAccountNumberVerify(
    PersonalBankAccountNumberVerify event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    try {
      emit(state.copyWith(isBankAccountNumberVerifiedLoading: true));

      final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

      final user = await Prefobj.preferences.get(Prefkeys.userDetail);
      final userDetail = jsonDecode(user!);

      final response = await _personalUserKycRepository.verifyBankAccount(
        userID: userId ?? '',
        userType: userDetail['user_type'],
        accountNumber: event.accountNumber,
        ifscCode: event.ifscCode,
      );
      if (response.success == true) {
        emit(
          state.copyWith(
            isBankAccountNumberVerifiedLoading: false,
            isBankAccountVerify: true,
            bankAccountNumber: event.accountNumber,
            ifscCode: event.ifscCode,
            accountHolderName: response.data?.accountHolderName ?? '', // from API ideally
          ),
        );
      } else {
        emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
    }
  }

  void _onPersonalUpdateBankAccountVerificationDocType(
    PersonalUpdateBankAccountVerificationDocType event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(selectedBankAccountVerificationDocType: event.selectedType));
  }

  void _onPersonalUploadBankAccountVerificationFile(
    PersonalUploadBankAccountVerificationFile event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(bankVerificationFile: event.fileData));
  }

  void _onPersonalBankAccountDetailSubmitted(
    PersonalBankAccountDetailSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isBankAccountNumberVerifiedLoading: true));

    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    final user = await Prefobj.preferences.get(Prefkeys.userDetail);
    final userDetail = jsonDecode(user!);

    try {
      final response = await _personalUserKycRepository.uploadBankDocuments(
        userID: userId ?? '',
        userType: userDetail['user_type'] ?? '',
        accountNumber: state.bankAccountNumber ?? '',
        documentType: event.documentType ?? '',
        ifscCode: state.ifscCode ?? '',
        proofDocumentImage: event.bankAccountVerifyFile,
      );
      if (response.success == true) {
        final currentIndex = state.currentKycVerificationStep.index;
        if (currentIndex < PersonalEKycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[currentIndex + 1]));
        }
        emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      } else {
        emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isBankAccountNumberVerifiedLoading: false));
    }
  }

  // void _onInitializeSelfie(InitializeSelfieEvent event, Emitter<PersonalAccountSetupState> emit) async {
  //   emit(
  //     state.copyWith(
  //       imageBytes: null,
  //       cameraController: null,
  //       hasPermission: true,
  //       errorMessage: null,
  //       isLoading: true,
  //     ),
  //   );

  //   try {
  //     _cameraController?.dispose();

  //     // Request camera permission
  //     final permissionStatus = await Permission.camera.request();

  //     if (permissionStatus == PermissionStatus.granted) {
  //       // Get available cameras
  //       _cameras = await availableCameras();

  //       if (_cameras != null && _cameras!.isNotEmpty) {
  //         // Initialize camera controller
  //         _cameraController = CameraController(_cameras![0], ResolutionPreset.high, enableAudio: false);

  //         await _cameraController!.initialize();

  //         emit(
  //           state.copyWith(
  //             isLoading: false,
  //             cameraController: _cameraController,
  //             hasPermission: true,
  //             errorMessage: null,
  //           ),
  //         );
  //       } else {
  //         emit(
  //           state.copyWith(isLoading: false, errorMessage: 'No cameras available on this device', hasPermission: true),
  //         );
  //       }
  //     } else {
  //       emit(
  //         state.copyWith(
  //           isLoading: false,
  //           errorMessage: 'Camera permission is required to use this feature',
  //           hasPermission: false,
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     emit(
  //       state.copyWith(
  //         isLoading: false,
  //         errorMessage: 'Failed to initialize camera: ${e.toString()}',
  //         hasPermission: false,
  //       ),
  //     );
  //   }
  // }

  // void _onCaptureImage(CaptureImageEvent event, Emitter<PersonalAccountSetupState> emit) async {
  //   if (_cameraController == null || !_cameraController!.value.isInitialized) {
  //     return;
  //   }

  //   try {
  //     emit(state.copyWith(isLoading: true));

  //     final XFile image = await _cameraController!.takePicture();
  //     final Uint8List imageBytes = await image.readAsBytes();

  //     _capturedImageBytes = imageBytes;

  //     emit(
  //       state.copyWith(
  //         isLoading: false,
  //         imageBytes: imageBytes,
  //         cameraController: _cameraController,
  //         hasPermission: true,
  //         errorMessage: null,
  //       ),
  //     );
  //   } catch (e) {
  //     emit(
  //       state.copyWith(isLoading: false, errorMessage: 'Failed to capture image: ${e.toString()}', hasPermission: true),
  //     );
  //   }
  // }

  // void _onRetakeImage(RetakeImageEvent event, Emitter<PersonalAccountSetupState> emit) async {
  //   _capturedImageBytes = null;

  //   try {
  //     // Dispose the current camera controller
  //     await _cameraController?.dispose();
  //     _cameraController = null;

  //     // Reinitialize the camera
  //     if (_cameras != null && _cameras!.isNotEmpty) {
  //       _cameraController = CameraController(_cameras![0], ResolutionPreset.high, enableAudio: false);
  //       await _cameraController!.initialize();

  //       emit(
  //         state.copyWith(
  //           imageBytes: null,
  //           cameraController: _cameraController,
  //           hasPermission: true,
  //           errorMessage: null,
  //           isLoading: false,
  //           isImageSubmitted: false,
  //         ),
  //       );
  //     } else {
  //       emit(
  //         state.copyWith(
  //           imageBytes: null,
  //           errorMessage: 'No cameras available on this device',
  //           hasPermission: true,
  //           isLoading: false,
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     emit(
  //       state.copyWith(
  //         imageBytes: null,
  //         errorMessage: 'Failed to reinitialize camera: ${e.toString()}',
  //         hasPermission: true,
  //         isLoading: false,
  //       ),
  //     );
  //   }
  // }

  // void _onSubmitImage(SubmitImageEvent event, Emitter<PersonalAccountSetupState> emit) async {
  //   if (_capturedImageBytes != null) {
  //     emit(state.copyWith(isLoading: true));

  //     try {
  //       await Future.delayed(const Duration(seconds: 1)); // Simulate processing

  //       emit(state.copyWith(isLoading: false, isImageSubmitted: true));

  //       // Optionally reset to camera ready state after submission
  //       await Future.delayed(const Duration(seconds: 2));
  //       _cameraController = CameraController(_cameras![0], ResolutionPreset.high, enableAudio: false);
  //       await _cameraController!.initialize();
  //       emit(
  //         state.copyWith(
  //           imageBytes: null,
  //           cameraController: _cameraController,
  //           hasPermission: true,
  //           errorMessage: null,
  //           isLoading: false,
  //           isImageSubmitted: false,
  //         ),
  //       );
  //     } catch (e) {
  //       emit(state.copyWith(isLoading: false, errorMessage: 'Failed to submit image: ${e.toString()}'));
  //     }
  //   }
  // }

  // void _onRequestPermission(RequestPermissionEvent event, Emitter<PersonalAccountSetupState> emit) {
  //   add(InitializeSelfieEvent());
  // }

  // void _onDisposeSelfie(DisposeSelfieEvent event, Emitter<PersonalAccountSetupState> emit) async {
  //   await _cameraController?.dispose();
  //   _cameraController = null;
  //   _capturedImageBytes = null;
  // }

  void _onScrollToSection(PersonalScrollToPosition event, Emitter<PersonalAccountSetupState> emit) {
    state.scrollDebounceTimer?.cancel();

    final scrollController = event.scrollController ?? state.scrollController;

    final newTimer = Timer(const Duration(milliseconds: 300), () {
      if (!scrollController.hasClients) {
        // If not attached, try to attach it
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!scrollController.hasClients) return;

          final RenderObject? renderObject = event.key.currentContext?.findRenderObject();
          if (renderObject != null) {
            scrollController.position.ensureVisible(
              renderObject,
              alignment: 0.06,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
            );
          }
        });
        return;
      }

      final RenderObject? renderObject = event.key.currentContext?.findRenderObject();
      if (renderObject != null) {
        scrollController.position.ensureVisible(
          renderObject,
          alignment: 0.06,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });

    emit(state.copyWith(scrollDebounceTimer: newTimer));
  }

  Future<void> _onSendOTP(SendOTP event, Emitter<PersonalAccountSetupState> emit) async {
    final mobileAvailability = await _authRepository.mobileAvailability(mobileNumber: state.mobileController.text);
    if (mobileAvailability.data?.exists == true) {
      AppToast.show(message: 'Mobile number already exists', type: ToastificationType.error);
      return;
    }
    emit(state.copyWith(isLoading: true));

    try {
      final response = await _authRepository.sendOtp(mobile: state.mobileController.text.trim(), type: 'registration');
      if (response.success == true) {
        emit(state.copyWith(isOTPSent: true, isLoading: false));
        _startResendTimer(emit);
        AppToast.show(message: response.message ?? '', type: ToastificationType.success);
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _onUpdateOTPError(UpdateOTPError event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(otpError: event.error));
  }

  Future<void> _onConfirmAndContinue(ConfirmAndContinue event, Emitter<PersonalAccountSetupState> emit) async {
    try {
      emit(state.copyWith(isLoading: true));
      final response = await _authRepository.validateregistrationOtp(
        mobile: state.mobileController.text.trim(),
        otp: state.otpController.text.trim(),
      );
      if (response.success == true) {
        add(
          UpdatePersonalDetails(
            fullName: state.fullNameController.text.trim(),
            website: state.websiteController.text.trim().isEmpty ? null : state.websiteController.text.trim(),
            phoneNumber: state.mobileController.text.trim(),
          ),
        );
        final index = state.currentStep.index;
        if (index < PersonalAccountSetupSteps.values.length - 1) {
          add(PersonalInfoStepChanged(PersonalAccountSetupSteps.values[index + 1]));
        }
        state.otpController.clear();
        _resendTimer?.cancel();

        emit(state.copyWith(isLoading: false, isVerifyPersonalRegisterdInfo: true));
        add(GetPersonalCurrencyOptions());
      } else {
        emit(state.copyWith(isLoading: false, isOTPSent: false));
        state.otpController.clear();
      }
    } catch (e) {
      emit(state.copyWith(isLoading: false));
    }
  }

  void _onUpdateResendTimerState(UpdateResendTimerState event, Emitter<PersonalAccountSetupState> emit) {
    emit(
      state.copyWith(timeLeft: event.timeLeft ?? state.timeLeft, canResendOTP: event.canResend ?? state.canResendOTP),
    );
  }

  void _startResendTimer(Emitter<PersonalAccountSetupState> emit) {
    _resendTimer?.cancel();
    int timeLeft = 120;

    emit(state.copyWith(timeLeft: timeLeft, canResendOTP: false));

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (isClosed) {
        timer.cancel();
        return;
      }

      timeLeft--;
      if (timeLeft < 0) {
        timer.cancel();
        add(UpdateResendTimerState(canResend: true));
      } else {
        add(UpdateResendTimerState(timeLeft: timeLeft));
      }
    });
  }

  void _onTogglePasswordVisibility(TogglePasswordVisibility event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(obscurePassword: !state.obscurePassword));
  }

  void _onToggleConfirmPasswordVisibility(
    ToggleConfirmPasswordVisibility event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    emit(state.copyWith(obscureConfirmPassword: !state.obscureConfirmPassword));
  }

  void _onPasswordChanged(PasswordChanged event, Emitter<PersonalAccountSetupState> emit) {
    state.passwordController.text = event.password;
    emit(state.copyWith());
  }

  void _onConfirmPasswordChanged(ConfirmPasswordChanged event, Emitter<PersonalAccountSetupState> emit) {
    state.confirmPasswordController.text = event.password;
    emit(state.copyWith());
  }

  void _onPersonalResetData(PersonalResetData event, Emitter<PersonalAccountSetupState> emit) {
    // Cancel all timers
    _timer?.cancel();
    _aadhartimer?.cancel();
    _resendTimer?.cancel();

    // Clear all text controllers
    state.professionOtherController.clear();
    state.productServiceDescriptionController.clear();
    state.passwordController.clear();
    state.confirmPasswordController.clear();
    state.aadharNumberController.clear();
    state.aadharOtpController.clear();
    state.drivingLicenceController.clear();
    state.voterIdNumberController.clear();
    state.passportNumberController.clear();
    state.panNameController.clear();
    state.panNumberController.clear();
    state.pinCodeController.clear();
    state.stateNameController.clear();
    state.cityNameController.clear();
    state.address1NameController.clear();
    state.address2NameController.clear();
    state.turnOverController.clear();
    state.gstNumberController.clear();
    state.bankAccountNumberController.clear();
    state.reEnterbankAccountNumberController.clear();
    state.ifscCodeController.clear();
    state.fullNameController.clear();
    state.websiteController.clear();
    state.mobileController.clear();
    state.otpController.clear();
    state.familyAndFriendsDescriptionController.clear();
    state.iceNumberController.clear();

    emit(
      PersonalAccountSetupState(
        familyAndFriendsDescriptionController: state.familyAndFriendsDescriptionController,
        currentStep: PersonalAccountSetupSteps.personalEntity,
        selectedPurpose: null,
        selectedProfession: null,
        fullName: null,
        email: null,
        website: null,
        phoneNumber: null,
        password: null,
        selectedEstimatedMonthlyTransaction: null,
        selectedCurrencies: [],
        isTransactionDetailLoading: false,
        currentKycVerificationStep: PersonalEKycVerificationSteps.identityVerification,
        selectedIDVerificationDocType: null,
        isOtpSent: false,
        aadharOtpRemainingTime: 0,
        isAadharOtpTimerRunning: false,
        aadharNumber: null,
        isIdVerifiedLoading: false,
        isIdVerified: false,
        drivingLicenseNumber: null,
        isDrivingIdVerifiedLoading: false,
        isDrivingIdVerified: false,
        drivingLicenceFrontSideFile: null,
        frontSideAdharFile: null,
        backSideAdharFile: null,
        isIdFileSubmittedLoading: false,
        voterIDNumber: null,
        isvoterIdVerifiedLoading: false,
        isvoterIdVerified: false,
        voterIdFileData: null,
        passporteNumber: null,
        ispassportIdVerifiedLoading: false,
        ispassportIdVerified: false,
        passportFileData: null,
        panFileData: null,
        isPanVerifyingLoading: false,
        selectedCountry: Country(
          phoneCode: '91',
          countryCode: 'IN',
          e164Sc: 0,
          geographic: true,
          level: 1,
          name: 'India',
          example: '**********',
          displayName: 'India',
          displayNameNoCountryCode: 'India',
          e164Key: '',
        ),
        selectedAddressVerificationDocType: state.selectedAddressVerificationDocType,
        addressVerificationFile: null,
        isAddressVerificationLoading: false,
        gstCertificateFile: null,
        isGstCertificateMandatory: false,
        isGstVerificationLoading: false,
        bankAccountNumber: null,
        ifscCode: null,
        accountHolderName: null,
        selectedBankAccountVerificationDocType: null,
        bankVerificationFile: null,
        isBankAccountVerify: false,
        isBankAccountNumberVerifiedLoading: false,
        isLoading: false,
        isReady: false,
        hasPermission: false,
        cameraController: null,
        imageBytes: null,
        errorMessage: null,
        isImageCaptured: false,
        isImageSubmitted: false,
        navigateNext: false,
        scrollDebounceTimer: null,
        otpError: null,
        isSignupSuccess: false,
        scrollController: state.scrollController,
        professionOtherController: state.professionOtherController,
        productServiceDescriptionController: state.productServiceDescriptionController,
        passwordController: state.passwordController,
        confirmPasswordController: state.confirmPasswordController,
        personalInfoKey: state.personalInfoKey,
        aadharNumberController: state.aadharNumberController,
        aadharOtpController: state.aadharOtpController,
        aadharVerificationFormKey: state.aadharVerificationFormKey,
        drivingVerificationFormKey: state.drivingVerificationFormKey,
        drivingLicenceController: state.drivingLicenceController,
        voterVerificationFormKey: state.voterVerificationFormKey,
        voterIdNumberController: state.voterIdNumberController,
        passportVerificationFormKey: state.passportVerificationFormKey,
        passportNumberController: state.passportNumberController,
        panVerificationKey: state.panVerificationKey,
        panNameController: state.panNameController,
        panNumberController: state.panNumberController,
        registerAddressFormKey: state.registerAddressFormKey,
        pinCodeController: state.pinCodeController,
        stateNameController: state.stateNameController,
        cityNameController: state.cityNameController,
        address1NameController: state.address1NameController,
        address2NameController: state.address2NameController,
        annualTurnoverFormKey: state.annualTurnoverFormKey,
        turnOverController: state.turnOverController,
        gstNumberController: state.gstNumberController,
        personalBankAccountVerificationFormKey: state.personalBankAccountVerificationFormKey,
        bankAccountNumberController: state.bankAccountNumberController,
        reEnterbankAccountNumberController: state.reEnterbankAccountNumberController,
        ifscCodeController: state.ifscCodeController,
        fullNameController: state.fullNameController,
        websiteController: state.websiteController,
        mobileController: state.mobileController,
        otpController: state.otpController,
        sePasswordFormKey: state.sePasswordFormKey,
        captchaInputController: state.captchaInputController,
        backAddressVerificationFile: null,
        currencyList: [],
        iceVerificationKey: state.iceVerificationKey,
        iceNumberController: state.iceNumberController,
        isLocalDataLoading: false,
      ),
    );
  }

  Future<void> _onGetPersonalCurrencyOptions(
    GetPersonalCurrencyOptions event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));
    try {
      final GetCurrencyOptionModel response = await _authRepository.getCurrencyOptions();
      if (response.success == true) {
        final List<CurrencyModel> currencyList =
            (response.data?.multicurrency ?? []).map((currency) {
              final parts = currency.split(' ');
              final symbol = parts[0];
              final name = parts.sublist(1).join(' ');
              return CurrencyModel(
                currencyName: name,
                currencySymbol: symbol,
                currencyImagePath:
                    symbol == "TRY" ? "assets/images/svgs/country/TRI.svg" : "assets/images/svgs/country/$symbol.svg",
              );
            }).toList();
        emit(
          state.copyWith(
            currencyList: currencyList,
            estimatedMonthlyVolumeList: response.data?.estimatedMonthlyVolume ?? [],
            isLoading: false,
          ),
        );
      } else {
        emit(state.copyWith(isLoading: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isLoading: false));
    }
  }

  void _onClearIdentityVerificationFields(
    PersonalClearIdentityVerificationFields event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    // Reset all fields and variables used in PersonalIdentityVerificationView
    state.aadharNumberController.clear();
    state.aadharOtpController.clear();
    state.captchaInputController.clear();
    state.drivingLicenceController.clear();
    state.voterIdNumberController.clear();
    state.passportNumberController.clear();

    emit(
      state.copyWith(
        isIdVerified: false,
        isIdVerifiedLoading: false,
        isCaptchaSend: false,
        isCaptchaLoading: false,
        captchaImage: '',
        isOtpSent: false,
        isOtpLoading: false,
        frontSideAdharFile: null,
        backSideAdharFile: null,
        drivingLicenceFrontSideFile: null,
        voterIdFileData: null,
        passportFileData: null,
        aadharNumber: null,
        drivingLicenseNumber: null,
        voterIDNumber: null,
        passporteNumber: null,
      ),
    );
  }

  void _onResidenceAddressSameAsAadhar(ResidenceAddressSameAsAadhar event, Emitter<PersonalAccountSetupState> emit) {
    emit(
      state.copyWith(
        isResidenceAddressSameAsAadhar: event.sameAddressSelected,
        addressVerificationFile: null,
        backAddressVerificationFile: null,
        selectedAddressVerificationDocType: '',
        pinCodeController: TextEditingController(),
        stateNameController: TextEditingController(),
        cityNameController: TextEditingController(),
        address1NameController: TextEditingController(),
        address2NameController: TextEditingController(),
      ),
    );
  }

  Future<void> _onGetPanDetails(GetPanDetails event, Emitter<PersonalAccountSetupState> emit) async {
    emit(state.copyWith(isPanDetailsLoading: true, isPanDetailsVerified: false));
    try {
      final userId = await Prefobj.preferences.get(Prefkeys.userId) ?? '';
      final GetPanDetailModel response = await _personalUserKycRepository.getPanDetails(
        panNumber: event.panNumber,
        userId: userId,
      );
      if (response.success == true) {
        final String panName = response.data?.nameInformation?.panNameCleaned ?? '';
        final String previousName = state.fullNameController.text.trim();
        final bool isDifferent = panName.isNotEmpty && panName.toLowerCase() != previousName.toLowerCase();
        if (isDifferent) {
          await Prefobj.preferences.put(Prefkeys.loggedUserName, panName);
        }
        emit(
          state.copyWith(
            isPanDetailsLoading: false,
            fullNamePan: panName,
            panNameController: TextEditingController(text: panName),
            isPanDetailsVerified: true,
            showPanNameOverwrittenPopup: isDifferent,
          ),
        );
      } else {
        emit(
          state.copyWith(isPanDetailsLoading: false, isPanDetailsVerified: false, showPanNameOverwrittenPopup: false),
        );
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isPanDetailsLoading: false, isPanDetailsVerified: false, showPanNameOverwrittenPopup: false));
    }
  }

  void _onPersonalPanNumberChanged(PersonalPanNumberChanged event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(isPanDetailsVerified: false));
  }

  Future<void> _onGetCityAndState(GetCityAndState event, Emitter<PersonalAccountSetupState> emit) async {
    emit(state.copyWith(isCityAndStateLoading: true, isCityAndStateVerified: false));
    try {
      final GetCityAndStateModel response = await _personalUserKycRepository.getCityAndState(pincode: event.pinCode);
      if (response.success == true) {
        emit(
          state.copyWith(
            isCityAndStateLoading: false,
            cityNameController: TextEditingController(text: response.data?.city ?? ''),
            stateNameController: TextEditingController(text: response.data?.state ?? ''),
            isCityAndStateVerified: true,
          ),
        );
      } else {
        emit(state.copyWith(isCityAndStateLoading: false, isCityAndStateVerified: false));
      }
    } catch (e) {
      Logger.error(e.toString());
      emit(state.copyWith(isCityAndStateLoading: false, isCityAndStateVerified: false));
    }
  }

  void _onChangeAgreeToAddressSameAsAadhar(
    ChangeAgreeToAddressSameAsAadhar event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    emit(state.copyWith(isAgreeToAddressSameAsAadhar: event.isAgree));
  }

  void _onPersonalAppBarCollapseChanged(PersonalAppBarCollapseChanged event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(isCollapsed: event.isCollapsed));
  }

  void _onPersonalEkycAppBarCollapseChanged(
    PersonalEkycAppBarCollapseChanged event,
    Emitter<PersonalAccountSetupState> emit,
  ) {
    emit(state.copyWith(isEkycCollapsed: event.isCollapsed));
  }

  void _onUploadICECertificate(PersonalUploadICECertificate event, Emitter<PersonalAccountSetupState> emit) {
    emit(state.copyWith(iceCertificateFile: event.fileData));
  }

  Future<void> _onICEVerificationSubmitted(
    PersonalICEVerificationSubmitted event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isIceVerifyingLoading: true));
    final String? userId = await Prefobj.preferences.get(Prefkeys.userId);

    try {
      final response = await _personalUserKycRepository.uploadBusinessLegalDocuments(
        userID: userId ?? '',
        userType: 'personal',
        documentType: 'IEC',
        documentNumber: event.iceNumber ?? '',
        documentFrontImage: event.fileData,
      );
      if (response.success == true) {
        final index = state.currentKycVerificationStep.index;
        if (index < PersonalEKycVerificationSteps.values.length - 1) {
          add(PersonalKycStepChange(PersonalEKycVerificationSteps.values[index + 1]));
        }
        emit(state.copyWith(isIceVerifyingLoading: false));
      } else {
        emit(state.copyWith(isIceVerifyingLoading: false));
      }
    } catch (e) {
      emit(state.copyWith(isIceVerifyingLoading: false));
    }
  }

  void _onPersonalAadharNumberChanged(
    PersonalAadharNumberChanged event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(
      state.copyWith(
        isCaptchaSend: false,
        isOtpSent: false,
        captchaImage: null,
        captchaInputController: TextEditingController(),
        aadharOtpController: TextEditingController(),
        isIdVerified: false,
        // Reset any other relevant fields
      ),
    );
  }

  void _onLoadPersonalKycFromLocal(LoadPersonalKycFromLocal event, Emitter<PersonalAccountSetupState> emit) async {
    add(PersonalResetData());
    await Future.delayed(Duration.zero); // Ensure the event is processed first
    emit(state.copyWith(isLocalDataLoading: true));
    final userJson = await Prefobj.preferences.get(Prefkeys.userDetail);
    if (userJson != null) {
      final userData = jsonDecode(userJson);
      // Personal details
      final personalDetails = userData['personal_details'] ?? {};

      // PAN
      final panDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'Pan',
        orElse: () => null,
      );
      // Address
      final address = userData['user_address_documents'] ?? {};
      // GST
      final gst = userData['user_gst_details'] ?? {};

      // Aadhar
      final aadhaarDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar',
        orElse: () => null,
      );

      // VoterID
      final voterDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'VoterID',
        orElse: () => null,
      );
      // Passport
      final passportDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'Passport',
        orElse: () => null,
      );
      // Driving Licence
      final drivingDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'DrivingLicense',
        orElse: () => null,
      );
      // ICE
      final iceDoc = (userData['user_business_legal_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'IEC',
        orElse: () => null,
      );

      // Helper to convert string to enum
      IDVerificationDocType? _docTypeFromString(String? type) {
        switch (type) {
          case 'Aadhaar':
            return IDVerificationDocType
                .aadharCard; // If you want to map Pan to aadharCard, otherwise return null or add Pan to enum
          case 'VoterID':
            return IDVerificationDocType.voterID;
          case 'Passport':
            return IDVerificationDocType.passport;
          case 'DrivingLicense':
            return IDVerificationDocType.drivingLicense;
          default:
            return null;
        }
      }

      // Set selectedIDVerificationDocType to the first available doc type
      String? selectedDocType;
      if (aadhaarDoc != null) {
        selectedDocType = 'Aadhaar';
      } else if (voterDoc != null) {
        selectedDocType = 'VoterID';
      } else if (passportDoc != null) {
        selectedDocType = 'Passport';
      } else if (drivingDoc != null) {
        selectedDocType = 'DrivingLicense';
      }

      final selectedIDVerificationDocType = _docTypeFromString(selectedDocType);

      // state.isvoterIdVerified = voterDoc != null ? true  : false;
      state.fullNameController.text = personalDetails['legal_full_name'] ?? '';
      state.productServiceDescriptionController.text = personalDetails['product_desc'] ?? '';
      // Fix: handle profession as List or String
      final profession = personalDetails['profession'];
      if (profession is List) {
        state.professionOtherController.text = profession.join(', ');
      } else if (profession is String) {
        state.professionOtherController.text = profession;
      } else {
        state.professionOtherController.text = '';
      }
      state.panNumberController.text = panDoc != null ? panDoc['document_number'] ?? '' : '';
      state.panNameController.text = panDoc != null ? panDoc['name_on_pan'] ?? '' : '';
      state.voterIdNumberController.text = voterDoc != null ? voterDoc['document_number'] ?? '' : '';
      state.passportNumberController.text = passportDoc != null ? passportDoc['document_number'] ?? '' : '';
      state.drivingLicenceController.text = drivingDoc != null ? drivingDoc['document_number'] ?? '' : '';
      state.iceNumberController.text = iceDoc != null ? iceDoc['document_number'] ?? '' : '';
      state.pinCodeController.text = address['pincode'] ?? '';
      state.stateNameController.text = address['state'] ?? '';
      state.cityNameController.text = address['city'] ?? '';
      state.address1NameController.text = address['address_line1'] ?? '';
      state.gstNumberController.text = gst['gst_number'] ?? '';
      state.turnOverController.text = gst['estimated_annual_income'] ?? '';
      state.aadharNumberController.text = aadhaarDoc != null ? aadhaarDoc['document_number'] ?? '' : '';

      // Now fetch and set FileData for all document file fields
      if (voterDoc != null && voterDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(voterDoc['front_doc_url'], 'VoterID');
        if (fileData != null) {
          emit(state.copyWith(voterIdFileData: fileData));
        }
      }
      if (panDoc != null && panDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(panDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(panFileData: fileData));
        }
      }
      if (passportDoc != null && passportDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(passportDoc['front_doc_url'], 'Passport');
        if (fileData != null) {
          emit(state.copyWith(passportFileData: fileData));
        }
      }
      if (drivingDoc != null && drivingDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(drivingDoc['front_doc_url'], 'DrivingLicense');
        if (fileData != null) {
          emit(state.copyWith(drivingLicenceFrontSideFile: fileData));
        }
      }
      if (aadhaarDoc != null && (aadhaarDoc['front_doc_url'] != '')) {
        final fileData = await getFileDataFromPath(aadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(frontSideAdharFile: fileData));
        }
      }

      if (aadhaarDoc != null && aadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(aadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(backSideAdharFile: fileData));
        }
      }

      if (address != null && address['front_doc_url'] != '') {
        final fileData = await getFileDataFromPath(address['front_doc_url'], address['document_type']);
        if (fileData != null) {
          emit(state.copyWith(addressVerificationFile: fileData));
        }

        // final backFileData = await getFileDataFromPath(address['back_doc_url'], 'Aadhaar');
        // if (backFileData != null) {
        //   emit(state.copyWith(backAddressVerificationFile: backFileData));
        // }
      }

      if (gst != null && gst['gst_certificate_url'] != '') {
        final fileData = await getFileDataFromPath(gst['gst_certificate_url'], "GST");
        if (fileData != null) {
          emit(state.copyWith(gstCertificateFile: fileData));
        }
      }

      if (iceDoc != null && iceDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(iceDoc['doc_url'], "ICE");
        if (fileData != null) {
          emit(state.copyWith(iceCertificateFile: fileData));
        }
      }

      // Determine which eKYC step is incomplete and set currentKycVerificationStep accordingly
      PersonalEKycVerificationSteps nextKycStep = state.currentKycVerificationStep;
      if (voterDoc == null && drivingDoc == null && passportDoc == null && aadhaarDoc == null) {
        nextKycStep = PersonalEKycVerificationSteps.identityVerification;
      } else if (state.panFileData == null || state.panNumberController.text.isEmpty) {
        nextKycStep = PersonalEKycVerificationSteps.panDetails;
      } else if (state.addressVerificationFile == null || state.pinCodeController.text.isEmpty) {
        nextKycStep = PersonalEKycVerificationSteps.residentialAddress;
      } else if (state.turnOverController.text.isEmpty) {
        nextKycStep = PersonalEKycVerificationSteps.annualTurnoverDeclaration;
      } else if (state.iceCertificateFile == null || state.iceNumberController.text.isEmpty) {
        nextKycStep = PersonalEKycVerificationSteps.iecVerification;
      } else if (state.bankVerificationFile == null || state.bankAccountNumberController.text.isEmpty) {
        nextKycStep = PersonalEKycVerificationSteps.bankAccountLinking;
      }

      print("gst :: ${gst['gst_number'] != ''}");

      emit(
        state.copyWith(
          fullName: personalDetails['legal_full_name'],
          selectedPurpose: personalDetails['payment_purpose'],
          productServiceDescriptionController: state.productServiceDescriptionController,
          professionOtherController: state.professionOtherController,
          panNumberController: state.panNumberController,
          panNameController: state.panNameController,
          voterIdNumberController: state.voterIdNumberController,
          passportNumberController: state.passportNumberController,
          drivingLicenceController: state.drivingLicenceController,
          iceNumberController: state.iceNumberController,
          pinCodeController: state.pinCodeController,
          stateNameController: state.stateNameController,
          cityNameController: state.cityNameController,
          address1NameController: state.address1NameController,
          gstNumberController: state.gstNumberController,
          turnOverController: state.turnOverController,
          selectedAnnualTurnover: gst['estimated_annual_income'],
          gstLegalName: gst['legal_name'],
          selectedIDVerificationDocType: selectedIDVerificationDocType,
          isvoterIdVerified: voterDoc != null ? true : false,
          isDrivingIdVerified: drivingDoc != null ? true : false,
          drivingLicenseNumber: drivingDoc != null ? drivingDoc['document_number'] ?? '' : '',
          ispassportIdVerified: passportDoc != null ? true : false,
          passporteNumber: passportDoc != null ? passportDoc['document_number'] ?? '' : '',
          voterIDNumber: voterDoc != null ? voterDoc['document_number'] ?? '' : '',
          isIdVerified:
              (voterDoc != null || aadhaarDoc != null || passportDoc != null || drivingDoc != null) ? true : false,
          isPanDetailsVerified: panDoc != null ? true : false,
          selectedAddressVerificationDocType:
              address != null
                  ? (address['document_type'] ?? '').replaceAllMapped(
                    RegExp(r'(?<!^)([A-Z])'),
                    (match) => ' ${match.group(1)}',
                  )
                  : '',
          isGstCertificateMandatory:
              gst != null ? (gst['estimated_annual_income'].contains("Less than") ? false : true) : false,
          isGSTNumberVerify: (gst['gst_number'] != '') ? true : false,
          currentKycVerificationStep: nextKycStep,
          aadharNumber: aadhaarDoc != null ? aadhaarDoc['document_number'] ?? '' : '',
          aadharNumberController: state.aadharNumberController,
        ),
      );

      emit(state.copyWith(isLocalDataLoading: false));
      event.completer?.complete();
    }
  }

  Future<void> _refreshKycFileData() async {
    final userJson = await Prefobj.preferences.get(Prefkeys.userDetail);
    if (userJson != null) {
      final userData = jsonDecode(userJson);
      final panDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'Pan',
        orElse: () => null,
      );
      final aadhaarDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'Aadhaar',
        orElse: () => null,
      );
      final voterDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'VoterID',
        orElse: () => null,
      );
      final passportDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'Passport',
        orElse: () => null,
      );
      final drivingDoc = (userData['user_identity_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'DrivingLicense',
        orElse: () => null,
      );

      final gst = userData['user_gst_details'] ?? {};

      final address = userData['user_address_documents'] ?? {};

      final iceDoc = (userData['user_business_legal_documents'] as List?)?.firstWhere(
        (doc) => doc['document_type'] == 'IEC',
        orElse: () => null,
      );

      if (voterDoc != null && voterDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(voterDoc['front_doc_url'], 'VoterID');
        if (fileData != null) {
          emit(state.copyWith(voterIdFileData: fileData));
        }
      }
      if (panDoc != null && panDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(panDoc['front_doc_url'], 'Pan');
        if (fileData != null) {
          emit(state.copyWith(panFileData: fileData));
        }
      }
      if (passportDoc != null && passportDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(passportDoc['front_doc_url'], 'Passport');
        if (fileData != null) {
          emit(state.copyWith(passportFileData: fileData));
        }
      }
      if (drivingDoc != null && drivingDoc['front_doc_url'] != null) {
        final fileData = await getFileDataFromPath(drivingDoc['front_doc_url'], 'DrivingLicense');
        if (fileData != null) {
          emit(state.copyWith(drivingLicenceFrontSideFile: fileData));
        }
      }
      if (aadhaarDoc != null && aadhaarDoc['front_doc_url'] != '') {
        final fileData = await getFileDataFromPath(aadhaarDoc['front_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(frontSideAdharFile: fileData));
        }
      }
      if (aadhaarDoc != null && aadhaarDoc['back_doc_url'] != null) {
        final fileData = await getFileDataFromPath(aadhaarDoc['back_doc_url'], 'Aadhaar');
        if (fileData != null) {
          emit(state.copyWith(backSideAdharFile: fileData));
        }
      }
      if (address != null && address['front_doc_url'] != '') {
        final fileData = await getFileDataFromPath(address['front_doc_url'], address['document_type']);
        if (fileData != null) {
          emit(state.copyWith(addressVerificationFile: fileData));
        }
      }

      if (gst != null && gst['gst_certificate_url'] != '') {
        final fileData = await getFileDataFromPath(gst['gst_certificate_url'], "GST");
        if (fileData != null) {
          emit(state.copyWith(gstCertificateFile: fileData));
        }
      }

      if (iceDoc != null && iceDoc['doc_url'] != null) {
        final fileData = await getFileDataFromPath(iceDoc['doc_url'], "ICE");
        if (fileData != null) {
          emit(state.copyWith(iceCertificateFile: fileData));
        }
      }
    }
  }

  Future<FileData?> getFileDataFromPath(String path, String fallbackName) async {
    try {
      final response = await _personalUserKycRepository.getPresignedUrl(urlPath: path);
      if (response.url != null) {
        Logger.success(response.url);
        // Extract the real file name from the URL
        final fileName = _extractFileNameFromUrl(response.url!) ?? fallbackName;
        return await downloadFileDataFromUrl(response.url!, fileName);
      }
    } catch (e) {
      Logger.error(e.toString());
    }
    return null;
  }

  Future<FileData?> downloadFileDataFromUrl(String url, String name) async {
    try {
      // Validate URL
      if (url.isEmpty) {
        Logger.error('Download error: Empty URL provided');
        return null;
      }

      final response = await Dio().get<List<int>>(
        url,
        options: Options(
          responseType: ResponseType.bytes,
          validateStatus: (status) => status != null && status < 500,
          receiveTimeout: const Duration(seconds: 30),
          // sendTimeout: const Duration(seconds: 30),
        ),
      );

      // Check if response is successful
      if (response.statusCode != 200) {
        Logger.error('Download error: HTTP ${response.statusCode} for URL: $url');
        return null;
      }

      // Validate response data
      if (response.data == null || response.data!.isEmpty) {
        Logger.error('Download error: Empty response data for URL: $url');
        return null;
      }

      // Validate data is actually bytes
      if (response.data is! List<int>) {
        Logger.error('Download error: Invalid response type for URL: $url');
        return null;
      }

      return FileData(
        name: name,
        bytes: Uint8List.fromList(response.data!),
        path: url,
        sizeInMB: response.data!.length / (1024 * 1024),
      );
    } on DioException catch (e) {
      Logger.error('Download error: DioException - ${e.message} for URL: $url');
      return null;
    } catch (e) {
      Logger.error('Download error: Unexpected error - $e for URL: $url');
      return null;
    }
  }

  String? _extractFileNameFromUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return null;
    return uri.pathSegments.isNotEmpty ? uri.pathSegments.last : null;
  }

  void _onPersonalChangeShowDescription(
    PersonalChangeShowDescription event,
    Emitter<PersonalAccountSetupState> emit,
  ) async {
    emit(state.copyWith(isShowServiceDescriptionBox: event.isShowDescriptionbox));
  }

  @override
  Future<void> close() async {
    _timer?.cancel();
    _aadhartimer?.cancel();
    _cameraController?.dispose();
    state.fullNameController.dispose();
    state.websiteController.dispose();
    state.mobileController.dispose();
    state.otpController.dispose();
    _resendTimer?.cancel();
    state.scrollController.dispose();
    await _cron?.close();
    return super.close();
  }
}
