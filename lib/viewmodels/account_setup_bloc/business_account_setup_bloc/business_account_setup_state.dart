part of 'business_account_setup_bloc.dart';

class BusinessAccountSetupState extends Equatable {
  final BusinessAccountSetupSteps currentStep;
  final String? selectedBusinessEntityType;
  final BusinessMainActivity? selectedBusinessMainActivity;
  final List<String>? selectedbusinessGoodsExportType;
  final List<String>? selectedbusinessServiceExportType;
  final TextEditingController goodsAndServiceExportDescriptionController;
  final TextEditingController goodsExportOtherController;
  final TextEditingController serviceExportOtherController;
  final TextEditingController businessActivityOtherController;
  final ScrollController scrollController;
  final Timer? scrollDebounceTimer;
  final GlobalKey<FormState> formKey;
  final TextEditingController businessLegalNameController;
  final TextEditingController professionalWebsiteUrl;
  final TextEditingController phoneController;
  final TextEditingController otpController;
  final int otpRemainingTime;
  final bool isOtpTimerRunning;
  final GlobalKey<FormState> sePasswordFormKey;
  final TextEditingController createPasswordController;
  final TextEditingController confirmPasswordController;
  final bool isCreatePasswordObscure;
  final bool isConfirmPasswordObscure;
  final bool? isSignupLoading;
  final bool? isSignupSuccess;
  final bool isBusinessInfoOtpSent;
  final KycVerificationSteps currentKycVerificationStep;
  final TextEditingController aadharNumberController;
  final TextEditingController aadharOtpController;
  final bool isOtpSent;
  final int aadharOtpRemainingTime;
  final bool isAadharOtpTimerRunning;
  final String? aadharNumber;
  final bool? isAadharVerifiedLoading;
  final bool? isAadharVerified;
  final GlobalKey<FormState> aadharVerificationFormKey;
  final FileData? frontSideAdharFile;
  final FileData? backSideAdharFile;
  final bool isAadharFileUploading;
  final TextEditingController kartaAadharNumberController;
  final TextEditingController kartaAadharOtpController;
  final bool isKartaOtpSent;
  final int kartaAadharOtpRemainingTime;
  final bool isKartaAadharOtpTimerRunning;
  final String? kartaAadharNumber;
  final bool? isKartaAadharVerifiedLoading;
  final bool? isKartaAadharVerified;
  final GlobalKey<FormState> kartaAadharVerificationFormKey;
  final FileData? kartaFrontSideAdharFile;
  final FileData? kartaBackSideAdharFile;
  final bool isKartaAadharFileUploading;
  final GlobalKey<FormState> hufPanVerificationKey;
  final TextEditingController hufPanNumberController;
  final FileData? hufPanCardFile;
  final bool? isHUFPanVerifyingLoading;
  final String? selectedUploadPanOption;
  final TextEditingController businessPanNumberController;
  final TextEditingController businessPanNameController;
  final FileData? businessPanCardFile;
  final bool? isBusinessPanCardSaveLoading;
  final bool? isBusinessPanCardSave;
  final GlobalKey<FormState> businessPanVerificationKey;
  final GlobalKey<FormState> directorsPanVerificationKey;
  final TextEditingController director1PanNumberController;
  final TextEditingController director1PanNameController;
  final bool director1BeneficialOwner;
  final bool ditector1BusinessRepresentative;
  final FileData? director1PanCardFile;
  final TextEditingController director2PanNumberController;
  final TextEditingController director2PanNameController;
  final FileData? director2PanCardFile;
  final bool director2BeneficialOwner;
  final bool ditector2BusinessRepresentative;
  final bool? isDirectorPanCardSaveLoading;
  final bool? isDirectorPanCardSave;
  final GlobalKey<FormState> beneficialOwnerPanVerificationKey;
  final TextEditingController beneficialOwnerPanNumberController;
  final TextEditingController beneficialOwnerPanNameController;
  final bool beneficialOwnerIsDirector;
  final bool benificialOwnerBusinessRepresentative;
  final FileData? beneficialOwnerPanCardFile;
  final bool? isBeneficialOwnerPanCardSaveLoading;
  final bool? isBeneficialOwnerPanCardSave;
  final GlobalKey<FormState> businessRepresentativeFormKey;
  final TextEditingController businessRepresentativePanNumberController;
  final TextEditingController businessRepresentativePanNameController;
  final bool businessRepresentativeIsDirector;
  final bool businessRepresentativeIsBenificalOwner;
  final FileData? businessRepresentativePanCardFile;
  final bool? isbusinessRepresentativePanCardSaveLoading;
  final bool? isbusinessRepresentativePanCardSave;
  final bool? isPanDetailVerifyLoading;
  final bool? isPanDetailVerifySuccess;
  final GlobalKey<FormState> registerAddressFormKey;
  final Country? selectedCountry;
  final TextEditingController pinCodeController;
  final TextEditingController stateNameController;
  final TextEditingController cityNameController;
  final TextEditingController address1NameController;
  final TextEditingController address2NameController;
  final FileData? addressVerificationFile;
  final String? selectedAddressVerificationDocType;
  final bool? isAddressVerificationLoading;
  final TextEditingController turnOverController;
  final TextEditingController gstNumberController;
  final FileData? gstCertificateFile;
  final bool? isGstVerificationLoading;
  final GlobalKey<FormState> annualTurnoverFormKey;
  final bool isGstCertificateMandatory;
  final GlobalKey<FormState> iceVerificationKey;
  final TextEditingController iceNumberController;
  final FileData? iceCertificateFile;
  final bool? isIceVerifyingLoading;
  final GlobalKey<FormState> cinVerificationKey;
  final TextEditingController cinNumberController;
  final FileData? coiCertificateFile;
  final bool? isCINVerifyingLoading;
  final TextEditingController llpinNumberController;
  final FileData? uploadLLPAgreementFile;
  final FileData? uploadPartnershipDeed;
  final bool isBankAccountVerify;
  final bool? isBankAccountNumberVerifiedLoading;
  final GlobalKey<FormState> bankAccountVerificationFormKey;
  final TextEditingController bankAccountNumberController;
  final TextEditingController reEnterbankAccountNumberController;
  final TextEditingController ifscCodeController;
  final String? accountHolderName;
  final FileData? bankVerificationFile;
  final String? selectedBankAccountVerificationDocType;
  final String? bankAccountNumber;
  final String? ifscCode;
  final bool? isBankAccountVerificationLoading;
  final String? selectedEstimatedMonthlyTransaction;
  final List<CurrencyModel>? curruncyList;
  final List<CurrencyModel>? selectedCurrencies;
  final bool? isTranscationDetailLoading;
  final bool isBusinessOtpValidating;
  final String? businessNatureString;
  final List<String>? estimatedMonthlyVolumeList;
  final String? selectedAnnualTurnover;
  final bool isCollapsed;
  final bool isekycCollapsed;
  final bool isDirectorCaptchaSend;
  final bool? isDirectorCaptchaLoading;
  final String? directorCaptchaImage;
  final TextEditingController directorCaptchaInputController;
  final bool isDirectorAadharOtpLoading;
  final String isAadharOTPInvalidate;
  final TextEditingController kartaCaptchaInputController;
  final String kartaCaptchaInput;
  final bool isKartaCaptchaValid;
  final bool isKartaCaptchaSubmitting;
  final String? kartaCaptchaError;
  final bool isKartaCaptchaSend;
  final bool? isKartaCaptchaLoading;
  final String? kartaCaptchaImage;
  final bool isKartaOtpLoading;
  final String? kartaIsAadharOTPInvalidate;
  final bool isSendOtpLoading;
  final bool isVerifyBusinessRegisterdInfo;
  final TextEditingController partnerAadharNumberController;
  final TextEditingController partnerAadharOtpController;
  final bool isPartnerOtpSent;
  final int partnerAadharOtpRemainingTime;
  final bool isPartnerAadharOtpTimerRunning;
  final String? partnerAadharNumber;
  final bool? isPartnerAadharVerifiedLoading;
  final bool? isPartnerAadharVerified;
  final GlobalKey<FormState> partnerAadharVerificationFormKey;
  final FileData? partnerFrontSideAdharFile;
  final FileData? partnerBackSideAdharFile;
  final bool isPartnerAadharFileUploading;
  final TextEditingController partnerCaptchaInputController;
  final String partnerCaptchaInput;
  final bool isPartnerCaptchaValid;
  final bool isPartnerCaptchaSubmitting;
  final String? partnerCaptchaError;
  final bool isPartnerCaptchaSend;
  final bool? isPartnerCaptchaLoading;
  final String? partnerCaptchaImage;
  final bool isPartnerOtpLoading;
  final String? partnerIsAadharOTPInvalidate;
  final TextEditingController proprietorAadharNumberController;
  final TextEditingController proprietorAadharOtpController;
  final bool isProprietorOtpSent;
  final int proprietorAadharOtpRemainingTime;
  final bool isProprietorAadharOtpTimerRunning;
  final String? proprietorAadharNumber;
  final bool? isProprietorAadharVerifiedLoading;
  final bool? isProprietorAadharVerified;
  final GlobalKey<FormState> proprietorAadharVerificationFormKey;
  final FileData? proprietorFrontSideAdharFile;
  final FileData? proprietorBackSideAdharFile;
  final bool isProprietorAadharFileUploading;
  final TextEditingController proprietorCaptchaInputController;
  final String proprietorCaptchaInput;
  final bool isProprietorCaptchaValid;
  final bool isProprietorCaptchaSubmitting;
  final String? proprietorCaptchaError;
  final bool isProprietorCaptchaSend;
  final bool? isProprietorCaptchaLoading;
  final String? proprietorCaptchaImage;
  final bool isProprietorOtpLoading;
  final String? proprietorIsAadharOTPInvalidate;
  final bool isLocalDataLoading;
  final bool isCityAndStateLoading;
  final bool isCityAndStateVerified;
  final String? gstLegalName;
  final bool isGSTNumberVerify;
  final bool isHUFPanDetailsLoading;
  final String? fullHUFNamePan;
  final bool isHUFPanDetailsVerified;
  final bool isDirector1PanDetailsLoading;
  final String? fullDirector1NamePan;
  final bool isDirector1PanDetailsVerified;
  final bool isDirector2PanDetailsLoading;
  final String? fullDirector2NamePan;
  final bool isDirector2PanDetailsVerified;
  final bool isBeneficialOwnerPanDetailsLoading;
  final String? fullBeneficialOwnerNamePan;
  final bool isBeneficialOwnerPanDetailsVerified;
  final bool isBusinessRepresentativePanDetailsLoading;
  final String? fullBusinessRepresentativeNamePan;
  final bool isBusinessRepresentativePanDetailsVerified;
  final TextEditingController directorMobileNumberController;
  final TextEditingController directorEmailIdNumberController;
  final GlobalKey<FormState> directorContactInformationKey;
  final bool isContactInfoSubmitLoading;
  final bool isAuthorizedDirectorKycVerify;
  final bool? isOtherDirectorPanCardSave;
  final bool isOtherDirectorKycVerify;
  final bool? isOtherDirectorPanCardSaveLoading;
  final GlobalKey<FormState> otherDirectorsPanVerificationKey;

  // Other Director Aadhar related properties
  final bool? isOtherAadharDirectorVerified;
  final GlobalKey<FormState> otherDirectorVerificationFormKey;
  final TextEditingController otherDirectorAadharNumberController;
  final TextEditingController otherDirectoraadharOtpController;
  final bool isOtherDirectorOtpSent;
  final bool isOtherDitectorOtpSent;
  final int otherDirectorAadharOtpRemainingTime;
  final bool isOtherDirectorAadharOtpTimerRunning;
  final String? otherDirectorAadharNumber;
  final bool? isOtherDirectorAadharVerifiedLoading;
  final bool? isOtherDirectorAadharVerified;
  final FileData? otherDirectorAadharfrontSideAdharFile;
  final FileData? otherDirectorAadharBackSideAdharFile;
  final bool isOtherDirectorAadharFileUploading;

  // Other Director Captcha related properties
  final bool isOtherDirectorCaptchaSend;
  final bool? isOtherDirectorDirectorCaptchaLoading;
  final String? otherDirectorCaptchaImage;
  final TextEditingController otherDirectorCaptchaInputController;
  final bool? isOtherDirectorAadharOtpLoading;
  final String isOtherAadharOTPInvalidate;

  BusinessAccountSetupState({
    required this.currentStep,
    this.selectedBusinessEntityType,
    this.selectedBusinessMainActivity,
    this.selectedbusinessGoodsExportType,
    this.selectedbusinessServiceExportType,
    required this.goodsAndServiceExportDescriptionController,
    required this.goodsExportOtherController,
    required this.serviceExportOtherController,
    required this.businessActivityOtherController,
    required this.scrollController,
    this.scrollDebounceTimer,
    required this.formKey,
    required this.businessLegalNameController,
    required this.professionalWebsiteUrl,
    required this.phoneController,
    required this.otpController,
    this.otpRemainingTime = 0,
    this.isOtpTimerRunning = false,
    required this.sePasswordFormKey,
    required this.createPasswordController,
    required this.confirmPasswordController,
    this.isCreatePasswordObscure = true,
    this.isConfirmPasswordObscure = true,
    this.isSignupLoading = false,
    this.isSignupSuccess = false,
    this.isBusinessInfoOtpSent = false,
    this.isOtpSent = false,
    this.aadharOtpRemainingTime = 0,
    this.isAadharOtpTimerRunning = false,
    required this.currentKycVerificationStep,
    required this.aadharNumberController,
    required this.aadharOtpController,
    this.aadharNumber,
    this.isAadharVerifiedLoading = false,
    this.isAadharVerified = false,
    required this.aadharVerificationFormKey,
    this.frontSideAdharFile,
    this.backSideAdharFile,
    this.isAadharFileUploading = false,
    this.isKartaOtpSent = false,
    required this.kartaAadharNumberController,
    required this.kartaAadharOtpController,
    this.kartaAadharOtpRemainingTime = 0,
    this.isKartaAadharOtpTimerRunning = false,
    this.kartaAadharNumber,
    this.isKartaAadharVerifiedLoading = false,
    this.isKartaAadharVerified = false,
    required this.kartaAadharVerificationFormKey,
    this.kartaFrontSideAdharFile,
    this.kartaBackSideAdharFile,
    this.isKartaAadharFileUploading = false,
    required this.hufPanVerificationKey,
    required this.hufPanNumberController,
    this.hufPanCardFile,
    required this.isHUFPanVerifyingLoading,
    this.selectedUploadPanOption,
    required this.businessPanNumberController,
    required this.businessPanNameController,
    this.businessPanCardFile,
    this.isBusinessPanCardSaveLoading = false,
    this.isBusinessPanCardSave = false,
    required this.businessPanVerificationKey,
    required this.directorsPanVerificationKey,
    required this.director1PanNumberController,
    required this.director1PanNameController,
    this.director1BeneficialOwner = false,
    this.ditector1BusinessRepresentative = false,
    this.director1PanCardFile,
    required this.director2PanNumberController,
    required this.director2PanNameController,
    this.director2PanCardFile,
    this.director2BeneficialOwner = false,
    this.ditector2BusinessRepresentative = false,
    this.isDirectorPanCardSaveLoading,
    this.isDirectorPanCardSave,
    required this.beneficialOwnerPanVerificationKey,
    required this.beneficialOwnerPanNumberController,
    required this.beneficialOwnerPanNameController,
    this.beneficialOwnerIsDirector = false,
    this.benificialOwnerBusinessRepresentative = false,
    this.beneficialOwnerPanCardFile,
    this.isBeneficialOwnerPanCardSaveLoading,
    this.isBeneficialOwnerPanCardSave,
    required this.businessRepresentativeFormKey,
    required this.businessRepresentativePanNumberController,
    required this.businessRepresentativePanNameController,
    this.businessRepresentativeIsDirector = false,
    this.businessRepresentativeIsBenificalOwner = false,
    this.businessRepresentativePanCardFile,
    this.isbusinessRepresentativePanCardSaveLoading,
    this.isbusinessRepresentativePanCardSave,
    this.isPanDetailVerifyLoading,
    this.isPanDetailVerifySuccess,
    required this.registerAddressFormKey,
    required this.selectedCountry,
    required this.pinCodeController,
    required this.stateNameController,
    required this.cityNameController,
    required this.address1NameController,
    required this.address2NameController,
    this.addressVerificationFile,
    this.selectedAddressVerificationDocType,
    this.isAddressVerificationLoading,
    required this.turnOverController,
    required this.gstNumberController,
    this.gstCertificateFile,
    this.isGstVerificationLoading,
    required this.annualTurnoverFormKey,
    required this.isGstCertificateMandatory,
    required this.iceVerificationKey,
    required this.iceNumberController,
    this.iceCertificateFile,
    this.isIceVerifyingLoading,
    required this.cinVerificationKey,
    required this.cinNumberController,
    this.coiCertificateFile,
    this.isCINVerifyingLoading,
    required this.llpinNumberController,
    this.uploadLLPAgreementFile,
    this.uploadPartnershipDeed,
    this.isBankAccountVerify = false,
    this.isBankAccountNumberVerifiedLoading,
    required this.bankAccountVerificationFormKey,
    required this.bankAccountNumberController,
    required this.reEnterbankAccountNumberController,
    required this.ifscCodeController,
    this.accountHolderName,
    this.bankVerificationFile,
    this.selectedBankAccountVerificationDocType,
    this.bankAccountNumber,
    this.ifscCode,
    this.isBankAccountVerificationLoading,
    this.selectedEstimatedMonthlyTransaction,
    this.curruncyList,
    this.selectedCurrencies,
    this.isTranscationDetailLoading,
    this.isBusinessOtpValidating = false,
    this.businessNatureString,
    this.estimatedMonthlyVolumeList,
    this.selectedAnnualTurnover,
    this.isCollapsed = false,
    this.isekycCollapsed = false,

    this.isDirectorCaptchaSend = false,
    this.isDirectorCaptchaLoading = false,
    this.directorCaptchaImage,
    required this.directorCaptchaInputController,
    this.isDirectorAadharOtpLoading = false,
    this.isAadharOTPInvalidate = '',
    required this.kartaCaptchaInputController,
    this.kartaCaptchaInput = '',
    this.isKartaCaptchaValid = false,
    this.isKartaCaptchaSubmitting = false,
    this.kartaCaptchaError,
    this.isKartaCaptchaSend = false,
    this.isKartaCaptchaLoading = false,
    this.kartaCaptchaImage,
    this.isKartaOtpLoading = false,
    this.kartaIsAadharOTPInvalidate,
    this.isSendOtpLoading = false,
    this.isVerifyBusinessRegisterdInfo = false,
    required this.partnerAadharNumberController,
    required this.partnerAadharOtpController,
    this.isPartnerOtpSent = false,
    this.partnerAadharOtpRemainingTime = 0,
    this.isPartnerAadharOtpTimerRunning = false,
    this.partnerAadharNumber,
    this.isPartnerAadharVerifiedLoading = false,
    this.isPartnerAadharVerified = false,
    required this.partnerAadharVerificationFormKey,
    this.partnerFrontSideAdharFile,
    this.partnerBackSideAdharFile,
    this.isPartnerAadharFileUploading = false,
    required this.partnerCaptchaInputController,
    this.partnerCaptchaInput = '',
    this.isPartnerCaptchaValid = false,
    this.isPartnerCaptchaSubmitting = false,
    this.partnerCaptchaError,
    this.isPartnerCaptchaSend = false,
    this.isPartnerCaptchaLoading = false,
    this.partnerCaptchaImage,
    this.isPartnerOtpLoading = false,
    this.partnerIsAadharOTPInvalidate,
    required this.proprietorAadharNumberController,
    required this.proprietorAadharOtpController,
    this.isProprietorOtpSent = false,
    this.proprietorAadharOtpRemainingTime = 0,
    this.isProprietorAadharOtpTimerRunning = false,
    this.proprietorAadharNumber,
    this.isProprietorAadharVerifiedLoading = false,
    this.isProprietorAadharVerified = false,
    required this.proprietorAadharVerificationFormKey,
    this.proprietorFrontSideAdharFile,
    this.proprietorBackSideAdharFile,
    this.isProprietorAadharFileUploading = false,
    required this.proprietorCaptchaInputController,
    this.proprietorCaptchaInput = '',
    this.isProprietorCaptchaValid = false,
    this.isProprietorCaptchaSubmitting = false,
    this.proprietorCaptchaError,
    this.isProprietorCaptchaSend = false,
    this.isProprietorCaptchaLoading = false,
    this.proprietorCaptchaImage,
    this.isProprietorOtpLoading = false,
    this.proprietorIsAadharOTPInvalidate,
    this.isLocalDataLoading = false,
    this.isCityAndStateLoading = false,
    this.isCityAndStateVerified = false,
    this.gstLegalName,
    this.isGSTNumberVerify = false,
    this.isHUFPanDetailsLoading = false,
    this.fullHUFNamePan,
    this.isHUFPanDetailsVerified = false,
    this.isDirector1PanDetailsLoading = false,
    this.fullDirector1NamePan,
    this.isDirector1PanDetailsVerified = false,
    this.isDirector2PanDetailsLoading = false,
    this.fullDirector2NamePan,
    this.isDirector2PanDetailsVerified = false,
    this.fullBeneficialOwnerNamePan,
    this.isBeneficialOwnerPanDetailsLoading = false,
    this.isBeneficialOwnerPanDetailsVerified = false,
    this.isBusinessRepresentativePanDetailsLoading = false,
    this.fullBusinessRepresentativeNamePan,
    this.isBusinessRepresentativePanDetailsVerified = false,
    required this.directorEmailIdNumberController,
    required this.directorMobileNumberController,
    required this.directorContactInformationKey,
    this.isContactInfoSubmitLoading = false,
    this.isAuthorizedDirectorKycVerify = false,
    this.isOtherDirectorPanCardSave = false,
    this.isOtherDirectorKycVerify = false,
    this.isOtherDirectorPanCardSaveLoading = false,
    required this.otherDirectorsPanVerificationKey,

    // Other Director Aadhar related properties
    this.isOtherAadharDirectorVerified = false,
    required this.otherDirectorVerificationFormKey,
    required this.otherDirectorAadharNumberController,
    required this.otherDirectoraadharOtpController,
    this.isOtherDirectorOtpSent = false,
    this.isOtherDitectorOtpSent = false,
    this.otherDirectorAadharOtpRemainingTime = 0,
    this.isOtherDirectorAadharOtpTimerRunning = false,
    this.otherDirectorAadharNumber,
    this.isOtherDirectorAadharVerifiedLoading = false,
    this.isOtherDirectorAadharVerified = false,
    this.otherDirectorAadharfrontSideAdharFile,
    this.otherDirectorAadharBackSideAdharFile,
    this.isOtherDirectorAadharFileUploading = false,

    // Other Director Captcha related properties
    this.isOtherDirectorCaptchaSend = false,
    this.isOtherDirectorDirectorCaptchaLoading = false,
    this.otherDirectorCaptchaImage,
    required this.otherDirectorCaptchaInputController,
    this.isOtherDirectorAadharOtpLoading = false,
    this.isOtherAadharOTPInvalidate = '',
  });

  @override
  List<Object?> get props => [
    currentStep,
    selectedBusinessEntityType,
    selectedBusinessMainActivity,
    selectedbusinessGoodsExportType,
    selectedbusinessServiceExportType,
    goodsAndServiceExportDescriptionController,
    goodsExportOtherController,
    serviceExportOtherController,
    businessActivityOtherController,
    scrollController,
    scrollDebounceTimer,
    formKey,
    businessLegalNameController,
    professionalWebsiteUrl,
    phoneController,
    otpController,
    otpRemainingTime,
    isOtpTimerRunning,
    sePasswordFormKey,
    createPasswordController,
    confirmPasswordController,
    isCreatePasswordObscure,
    isConfirmPasswordObscure,
    isSignupLoading,
    isSignupSuccess,
    isBusinessInfoOtpSent,
    isOtpSent,
    aadharOtpRemainingTime,
    isAadharOtpTimerRunning,
    currentKycVerificationStep,
    aadharNumberController,
    aadharOtpController,
    aadharNumber,
    isAadharVerifiedLoading,
    isAadharVerified,
    aadharVerificationFormKey,
    frontSideAdharFile,
    backSideAdharFile,
    isAadharFileUploading,
    isKartaOtpSent,
    kartaAadharNumberController,
    kartaAadharOtpController,
    kartaAadharOtpRemainingTime,
    isKartaAadharOtpTimerRunning,
    kartaAadharNumber,
    isKartaAadharVerifiedLoading,
    isKartaAadharVerified,
    kartaAadharVerificationFormKey,
    kartaFrontSideAdharFile,
    kartaBackSideAdharFile,
    isKartaAadharFileUploading,
    hufPanVerificationKey,
    hufPanNumberController,
    hufPanCardFile,
    isHUFPanVerifyingLoading,
    selectedUploadPanOption,
    businessPanNumberController,
    businessPanNameController,
    businessPanCardFile,
    isBusinessPanCardSaveLoading,
    isBusinessPanCardSave,
    businessPanVerificationKey,
    directorsPanVerificationKey,
    director1PanNumberController,
    director1PanNameController,
    director1BeneficialOwner,
    ditector1BusinessRepresentative,
    director1PanCardFile,
    director2PanNumberController,
    director2PanNameController,
    director2PanCardFile,
    director2BeneficialOwner,
    ditector2BusinessRepresentative,
    isDirectorPanCardSaveLoading,
    isDirectorPanCardSave,
    beneficialOwnerPanVerificationKey,
    beneficialOwnerPanNumberController,
    beneficialOwnerPanNameController,
    beneficialOwnerIsDirector,
    benificialOwnerBusinessRepresentative,
    beneficialOwnerPanCardFile,
    isBeneficialOwnerPanCardSaveLoading,
    isBeneficialOwnerPanCardSave,
    businessRepresentativeFormKey,
    businessRepresentativePanNumberController,
    businessRepresentativePanNameController,
    businessRepresentativeIsDirector,
    businessRepresentativeIsBenificalOwner,
    businessRepresentativePanCardFile,
    isbusinessRepresentativePanCardSaveLoading,
    isbusinessRepresentativePanCardSave,
    isPanDetailVerifyLoading,
    isPanDetailVerifySuccess,
    registerAddressFormKey,
    selectedCountry,
    pinCodeController,
    stateNameController,
    cityNameController,
    address1NameController,
    address2NameController,
    addressVerificationFile,
    isAddressVerificationLoading,
    turnOverController,
    gstNumberController,
    gstCertificateFile,
    isGstVerificationLoading,
    annualTurnoverFormKey,
    isGstCertificateMandatory,
    iceNumberController,
    iceVerificationKey,
    isIceVerifyingLoading,
    iceCertificateFile,
    cinNumberController,
    cinVerificationKey,
    coiCertificateFile,
    isCINVerifyingLoading,
    llpinNumberController,
    uploadLLPAgreementFile,
    uploadPartnershipDeed,
    isBankAccountVerify,
    isBankAccountNumberVerifiedLoading,
    bankAccountVerificationFormKey,
    bankAccountNumberController,
    reEnterbankAccountNumberController,
    ifscCodeController,
    accountHolderName,
    bankVerificationFile,
    selectedBankAccountVerificationDocType,
    bankAccountNumber,
    ifscCode,
    isBankAccountVerificationLoading,
    selectedEstimatedMonthlyTransaction,
    curruncyList,
    selectedCurrencies,
    isTranscationDetailLoading,
    isBusinessOtpValidating,
    businessNatureString,
    estimatedMonthlyVolumeList,
    selectedAnnualTurnover,
    isCollapsed,
    isekycCollapsed,
    isDirectorCaptchaSend,
    isDirectorCaptchaLoading,
    directorCaptchaImage,
    directorCaptchaInputController,
    isDirectorAadharOtpLoading,
    isAadharOTPInvalidate,
    kartaCaptchaInputController,
    kartaCaptchaInput,
    isKartaCaptchaValid,
    isKartaCaptchaSubmitting,
    kartaCaptchaError,
    isKartaCaptchaSend,
    isKartaCaptchaLoading,
    kartaCaptchaImage,
    isKartaOtpLoading,
    kartaIsAadharOTPInvalidate,
    isSendOtpLoading,
    isVerifyBusinessRegisterdInfo,
    partnerAadharNumberController,
    partnerAadharOtpController,
    isPartnerOtpSent,
    partnerAadharOtpRemainingTime,
    isPartnerAadharOtpTimerRunning,
    partnerAadharNumber,
    isPartnerAadharVerifiedLoading,
    isPartnerAadharVerified,
    partnerAadharVerificationFormKey,
    partnerFrontSideAdharFile,
    partnerBackSideAdharFile,
    isPartnerAadharFileUploading,
    partnerCaptchaInputController,
    partnerCaptchaInput,
    isPartnerCaptchaValid,
    isPartnerCaptchaSubmitting,
    partnerCaptchaError,
    isPartnerCaptchaSend,
    isPartnerCaptchaLoading,
    partnerCaptchaImage,
    isPartnerOtpLoading,
    partnerIsAadharOTPInvalidate,
    proprietorAadharNumberController,
    proprietorAadharOtpController,
    isProprietorOtpSent,
    proprietorAadharOtpRemainingTime,
    isProprietorAadharOtpTimerRunning,
    proprietorAadharNumber,
    isProprietorAadharVerifiedLoading,
    isProprietorAadharVerified,
    proprietorAadharVerificationFormKey,
    proprietorFrontSideAdharFile,
    proprietorBackSideAdharFile,
    isProprietorAadharFileUploading,
    proprietorCaptchaInputController,
    proprietorCaptchaInput,
    isProprietorCaptchaValid,
    isProprietorCaptchaSubmitting,
    proprietorCaptchaError,
    isProprietorCaptchaSend,
    isProprietorCaptchaLoading,
    proprietorCaptchaImage,
    isProprietorOtpLoading,
    proprietorIsAadharOTPInvalidate,
    isLocalDataLoading,
    selectedAddressVerificationDocType,
    isCityAndStateLoading,
    isCityAndStateVerified,
    gstLegalName,
    isGSTNumberVerify,
    isHUFPanDetailsLoading,
    fullHUFNamePan,
    isHUFPanDetailsVerified,
    isDirector1PanDetailsLoading,
    fullDirector1NamePan,
    isDirector1PanDetailsVerified,
    isDirector2PanDetailsLoading,
    fullDirector2NamePan,
    isDirector2PanDetailsVerified,
    fullBeneficialOwnerNamePan,
    isBeneficialOwnerPanDetailsLoading,
    isBeneficialOwnerPanDetailsVerified,
    isBusinessRepresentativePanDetailsLoading,
    fullBusinessRepresentativeNamePan,
    isBusinessRepresentativePanDetailsVerified,
    directorEmailIdNumberController,
    directorMobileNumberController,
    directorContactInformationKey,
    isContactInfoSubmitLoading,
    isAuthorizedDirectorKycVerify,
    isOtherDirectorPanCardSave,
    isOtherDirectorKycVerify,
    isOtherDirectorPanCardSaveLoading,
    otherDirectorsPanVerificationKey,

    // Other Director Aadhar related properties
    isOtherAadharDirectorVerified,
    otherDirectorVerificationFormKey,
    otherDirectorAadharNumberController,
    otherDirectoraadharOtpController,
    isOtherDirectorOtpSent,
    isOtherDitectorOtpSent,
    otherDirectorAadharOtpRemainingTime,
    isOtherDirectorAadharOtpTimerRunning,
    otherDirectorAadharNumber,
    isOtherDirectorAadharVerifiedLoading,
    isOtherDirectorAadharVerified,
    otherDirectorAadharfrontSideAdharFile,
    otherDirectorAadharBackSideAdharFile,
    isOtherDirectorAadharFileUploading,

    // Other Director Captcha related properties
    isOtherDirectorCaptchaSend,
    isOtherDirectorDirectorCaptchaLoading,
    otherDirectorCaptchaImage,
    otherDirectorCaptchaInputController,
    isOtherDirectorAadharOtpLoading,
    isOtherAadharOTPInvalidate,
  ];

  BusinessAccountSetupState copyWith({
    BusinessAccountSetupSteps? currentStep,
    List<String>? businessEntityList,
    String? selectedBusinessEntityType,
    BusinessMainActivity? selectedBusinessMainActivity,
    List<String>? selectedbusinessGoodsExportType,
    List<String>? selectedbusinessServiceExportType,
    TextEditingController? goodsAndServiceExportDescriptionController,
    TextEditingController? goodsExportOtherController,
    TextEditingController? serviceExportOtherController,
    TextEditingController? businessActivityOtherController,
    ScrollController? scrollController,
    Timer? scrollDebounceTimer,
    GlobalKey<FormState>? formKey,
    TextEditingController? businessLegalNameController,
    TextEditingController? professionalWebsiteUrl,
    TextEditingController? phoneController,
    TextEditingController? otpController,
    int? otpRemainingTime,
    bool? isOtpTimerRunning,
    GlobalKey<FormState>? sePasswordFormKey,
    TextEditingController? createPasswordController,
    TextEditingController? confirmPasswordController,
    bool? isCreatePasswordObscure,
    bool? isConfirmPasswordObscure,
    bool? isSignupLoading,
    bool? isSignupSuccess,
    bool? isBusinessInfoOtpSent,
    bool? isOtpSent,
    int? aadharOtpRemainingTime,
    bool? isAadharOtpTimerRunning,
    KycVerificationSteps? currentKycVerificationStep,
    TextEditingController? aadharNumberController,
    TextEditingController? aadharOtpController,
    String? aadharNumber,
    bool? isAadharVerifiedLoading,
    bool? isAadharVerified,
    GlobalKey<FormState>? aadharVerificationFormKey,
    FileData? frontSideAdharFile,
    FileData? backSideAdharFile,
    bool? isAadharFileUploading,
    TextEditingController? kartaAadharNumberController,
    TextEditingController? kartaAadharOtpController,
    bool? isKartaOtpSent,
    int? kartaAadharOtpRemainingTime,
    bool? isKartaAadharOtpTimerRunning,
    String? kartaAadharNumber,
    bool? isKartaAadharVerifiedLoading,
    bool? isKartaAadharVerified,
    GlobalKey<FormState>? kartaAadharVerificationFormKey,
    FileData? kartaFrontSideAdharFile,
    FileData? kartaBackSideAdharFile,
    bool? isKartaAadharFileUploading,
    GlobalKey<FormState>? hufPanVerificationKey,
    TextEditingController? hufPanNumberController,
    FileData? hufPanCardFile,
    bool? isHUFPanVerifyingLoading,
    String? selectedUploadPanOption,
    TextEditingController? businessPanNumberController,
    TextEditingController? businessPanNameController,
    FileData? businessPanCardFile,
    bool? isBusinessPanCardSaveLoading,
    bool? isBusinessPanCardSave,
    GlobalKey<FormState>? businessPanVerificationKey,
    GlobalKey<FormState>? directorsPanVerificationKey,
    TextEditingController? director1PanNumberController,
    TextEditingController? director1PanNameController,
    bool? director1BeneficialOwner,
    bool? ditector1BusinessRepresentative,
    FileData? director1PanCardFile,
    TextEditingController? director2PanNumberController,
    TextEditingController? director2PanNameController,
    FileData? director2PanCardFile,
    bool? director2BeneficialOwner,
    bool? ditector2BusinessRepresentative,
    bool? isDirectorPanCardSaveLoading,
    bool? isDirectorPanCardSave,
    GlobalKey<FormState>? beneficialOwnerPanVerificationKey,
    TextEditingController? beneficialOwnerPanNumberController,
    TextEditingController? beneficialOwnerPanNameController,
    bool? beneficialOwnerIsDirector,
    bool? benificialOwnerBusinessRepresentative,
    FileData? beneficialOwnerPanCardFile,
    bool? isBeneficialOwnerPanCardSaveLoading,
    bool? isBeneficialOwnerPanCardSave,
    GlobalKey<FormState>? businessRepresentativeFormKey,
    TextEditingController? businessRepresentativePanNumberController,
    TextEditingController? businessRepresentativePanNameController,
    bool? businessRepresentativeIsDirector,
    bool? businessRepresentativeIsBenificalOwner,
    FileData? businessRepresentativePanCardFile,
    bool? isbusinessRepresentativePanCardSaveLoading,
    bool? isbusinessRepresentativePanCardSave,
    bool? isPanDetailVerifyLoading,
    bool? isPanDetailVerifySuccess,
    GlobalKey<FormState>? registerAddressFormKey,
    Country? selectedCountry,
    TextEditingController? pinCodeController,
    TextEditingController? stateNameController,
    TextEditingController? cityNameController,
    TextEditingController? address1NameController,
    TextEditingController? address2NameController,
    FileData? addressVerificationFile,
    String? selectedAddressVerificationDocType,
    bool? isAddressVerificationLoading,
    TextEditingController? turnOverController,
    TextEditingController? gstNumberController,
    FileData? gstCertificateFile,
    bool? isGstVerificationLoading,
    GlobalKey<FormState>? annualTurnoverFormKey,
    bool? isGstCertificateMandatory,
    GlobalKey<FormState>? iceVerificationKey,
    TextEditingController? iceNumberController,
    FileData? iceCertificateFile,
    bool? isIceVerifyingLoading,
    GlobalKey<FormState>? cinVerificationKey,
    TextEditingController? cinNumberController,
    FileData? coiCertificateFile,
    bool? isCINVerifyingLoading,
    TextEditingController? llpinNumberController,
    FileData? uploadLLPAgreementFile,
    FileData? uploadPartnershipDeed,
    bool? isBankAccountVerify,
    bool? isBankAccountNumberVerifiedLoading,
    GlobalKey<FormState>? bankAccountVerificationFormKey,
    TextEditingController? bankAccountNumberController,
    TextEditingController? ifscCodeController,
    String? accountHolderName,
    FileData? bankVerificationFile,
    String? selectedBankAccountVerificationDocType,
    TextEditingController? reEnterbankAccountNumberController,
    String? bankAccountNumber,
    String? ifscCode,
    bool? isBankAccountVerificationLoading,
    String? selectedEstimatedMonthlyTransaction,
    List<CurrencyModel>? curruncyList,
    List<CurrencyModel>? selectedCurrencies,
    bool? isTranscationDetailLoading,
    bool? isBusinessOtpValidating,
    String? businessNatureString,
    List<String>? estimatedMonthlyVolumeList,
    String? selectedAnnualTurnover,
    bool? isCollapsed,
    bool? isekycCollapsed,
    bool? isDirectorCaptchaSend,
    bool? isDirectorCaptchaLoading,
    String? directorCaptchaImage,
    TextEditingController? directorCaptchaInputController,
    bool? isDirectorAadharOtpLoading,
    String? isAadharOTPInvalidate,
    TextEditingController? kartaCaptchaInputController,
    String? kartaCaptchaInput,
    bool? isKartaCaptchaValid,
    bool? isKartaCaptchaSubmitting,
    String? kartaCaptchaError,
    bool? isKartaCaptchaSend,
    bool? isKartaCaptchaLoading,
    String? kartaCaptchaImage,
    bool? isKartaOtpLoading,
    String? kartaIsAadharOTPInvalidate,
    bool? isSendOtpLoading,
    bool? isVerifyBusinessRegisterdInfo,
    TextEditingController? partnerAadharNumberController,
    TextEditingController? partnerAadharOtpController,
    bool? isPartnerOtpSent,
    int? partnerAadharOtpRemainingTime,
    bool? isPartnerAadharOtpTimerRunning,
    String? partnerAadharNumber,
    bool? isPartnerAadharVerifiedLoading,
    bool? isPartnerAadharVerified,
    GlobalKey<FormState>? partnerAadharVerificationFormKey,
    FileData? partnerFrontSideAdharFile,
    FileData? partnerBackSideAdharFile,
    bool? isPartnerAadharFileUploading,
    TextEditingController? partnerCaptchaInputController,
    String? partnerCaptchaInput,
    bool? isPartnerCaptchaValid,
    bool? isPartnerCaptchaSubmitting,
    String? partnerCaptchaError,
    bool? isPartnerCaptchaSend,
    bool? isPartnerCaptchaLoading,
    String? partnerCaptchaImage,
    bool? isPartnerOtpLoading,
    String? partnerIsAadharOTPInvalidate,
    TextEditingController? proprietorAadharNumberController,
    TextEditingController? proprietorAadharOtpController,
    bool? isProprietorOtpSent,
    int? proprietorAadharOtpRemainingTime,
    bool? isProprietorAadharOtpTimerRunning,
    String? proprietorAadharNumber,
    bool? isProprietorAadharVerifiedLoading,
    bool? isProprietorAadharVerified,
    GlobalKey<FormState>? proprietorAadharVerificationFormKey,
    FileData? proprietorFrontSideAdharFile,
    FileData? proprietorBackSideAdharFile,
    bool? isProprietorAadharFileUploading,
    TextEditingController? proprietorCaptchaInputController,
    String? proprietorCaptchaInput,
    bool? isProprietorCaptchaValid,
    bool? isProprietorCaptchaSubmitting,
    String? proprietorCaptchaError,
    bool? isProprietorCaptchaSend,
    bool? isProprietorCaptchaLoading,
    String? proprietorCaptchaImage,
    bool? isProprietorOtpLoading,
    String? proprietorIsAadharOTPInvalidate,
    bool? isLocalDataLoading,
    bool? isCityAndStateLoading,
    bool? isCityAndStateVerified,
    String? gstLegalName,
    bool? isGSTNumberVerify,
    bool? isHUFPanDetailsLoading,
    String? fullHUFNamePan,
    bool? isHUFPanDetailsVerified,
    bool? isDirector1PanDetailsLoading,
    String? fullDirector1NamePan,
    bool? isDirector1PanDetailsVerified,
    bool? isDirector2PanDetailsLoading,
    String? fullDirector2NamePan,
    bool? isDirector2PanDetailsVerified,
    bool? isBeneficialOwnerPanDetailsLoading,
    String? fullBeneficialOwnerNamePan,
    bool? isBeneficialOwnerPanDetailsVerified,
    bool? isBusinessRepresentativePanDetailsLoading,
    String? fullBusinessRepresentativeNamePan,
    bool? isBusinessRepresentativePanDetailsVerified,
    TextEditingController? directorMobileNumberController,
    TextEditingController? directorEmailIdNumberController,
    GlobalKey<FormState>? directorContactInformationKey,
    bool? isContactInfoSubmitLoading,
    bool? isAuthorizedDirectorKycVerify,
    bool? isOtherDirectorPanCardSave,
    bool? isOtherDirectorKycVerify,
    final bool? isOtherDirectorPanCardSaveLoading,
    GlobalKey<FormState>? otherDirectorsPanVerificationKey,

    // Other Director Aadhar related properties
    bool? isOtherAadharDirectorVerified,
    GlobalKey<FormState>? otherDirectorVerificationFormKey,
    TextEditingController? otherDirectorAadharNumberController,
    TextEditingController? otherDirectoraadharOtpController,
    bool? isOtherDirectorOtpSent,
    bool? isOtherDitectorOtpSent,
    int? otherDirectorAadharOtpRemainingTime,
    bool? isOtherDirectorAadharOtpTimerRunning,
    String? otherDirectorAadharNumber,
    bool? isOtherDirectorAadharVerifiedLoading,
    bool? isOtherDirectorAadharVerified,
    FileData? otherDirectorAadharfrontSideAdharFile,
    FileData? otherDirectorAadharBackSideAdharFile,
    bool? isOtherDirectorAadharFileUploading,

    // Other Director Captcha related properties
    bool? isOtherDirectorCaptchaSend,
    bool? isOtherDirectorDirectorCaptchaLoading,
    String? otherDirectorCaptchaImage,
    TextEditingController? otherDirectorCaptchaInputController,
    bool? isOtherDirectorAadharOtpLoading,
    String? isOtherAadharOTPInvalidate,
  }) {
    return BusinessAccountSetupState(
      currentStep: currentStep ?? this.currentStep,
      selectedBusinessEntityType: selectedBusinessEntityType ?? this.selectedBusinessEntityType,
      selectedBusinessMainActivity: selectedBusinessMainActivity ?? this.selectedBusinessMainActivity,
      selectedbusinessGoodsExportType: selectedbusinessGoodsExportType ?? this.selectedbusinessGoodsExportType,
      selectedbusinessServiceExportType: selectedbusinessServiceExportType ?? this.selectedbusinessServiceExportType,
      goodsAndServiceExportDescriptionController:
          goodsAndServiceExportDescriptionController ?? this.goodsAndServiceExportDescriptionController,
      goodsExportOtherController: goodsExportOtherController ?? this.goodsExportOtherController,
      serviceExportOtherController: serviceExportOtherController ?? this.serviceExportOtherController,
      businessActivityOtherController: businessActivityOtherController ?? this.businessActivityOtherController,
      scrollController: scrollController ?? this.scrollController,
      scrollDebounceTimer: scrollDebounceTimer ?? this.scrollDebounceTimer,
      formKey: formKey ?? this.formKey,
      businessLegalNameController: businessLegalNameController ?? this.businessLegalNameController,
      professionalWebsiteUrl: professionalWebsiteUrl ?? this.professionalWebsiteUrl,
      phoneController: phoneController ?? this.phoneController,
      otpController: otpController ?? this.otpController,
      otpRemainingTime: otpRemainingTime ?? this.otpRemainingTime,
      isOtpTimerRunning: isOtpTimerRunning ?? this.isOtpTimerRunning,
      sePasswordFormKey: sePasswordFormKey ?? this.sePasswordFormKey,
      createPasswordController: createPasswordController ?? this.createPasswordController,
      confirmPasswordController: confirmPasswordController ?? this.confirmPasswordController,
      isCreatePasswordObscure: isCreatePasswordObscure ?? this.isCreatePasswordObscure,
      isConfirmPasswordObscure: isConfirmPasswordObscure ?? this.isConfirmPasswordObscure,
      isSignupLoading: isSignupLoading ?? this.isSignupLoading,
      isSignupSuccess: isSignupSuccess ?? this.isSignupSuccess,
      isBusinessInfoOtpSent: isBusinessInfoOtpSent ?? this.isBusinessInfoOtpSent,
      isOtpSent: isOtpSent ?? this.isOtpSent,
      aadharOtpRemainingTime: aadharOtpRemainingTime ?? this.aadharOtpRemainingTime,
      isAadharOtpTimerRunning: isAadharOtpTimerRunning ?? this.isAadharOtpTimerRunning,
      currentKycVerificationStep: currentKycVerificationStep ?? this.currentKycVerificationStep,
      aadharNumberController: aadharNumberController ?? this.aadharNumberController,
      aadharOtpController: aadharOtpController ?? this.aadharOtpController,
      aadharNumber: aadharNumber ?? this.aadharNumber,
      isAadharVerifiedLoading: isAadharVerifiedLoading ?? this.isAadharVerifiedLoading,
      isAadharVerified: isAadharVerified ?? this.isAadharVerified,
      aadharVerificationFormKey: aadharVerificationFormKey ?? this.aadharVerificationFormKey,
      frontSideAdharFile: frontSideAdharFile ?? this.frontSideAdharFile,
      backSideAdharFile: backSideAdharFile ?? this.backSideAdharFile,
      isAadharFileUploading: isAadharFileUploading ?? this.isAadharFileUploading,
      kartaAadharNumberController: kartaAadharNumberController ?? this.kartaAadharNumberController,
      kartaAadharOtpController: kartaAadharOtpController ?? this.kartaAadharOtpController,
      isKartaOtpSent: isKartaOtpSent ?? this.isKartaOtpSent,
      kartaAadharOtpRemainingTime: kartaAadharOtpRemainingTime ?? this.kartaAadharOtpRemainingTime,
      isKartaAadharOtpTimerRunning: isKartaAadharOtpTimerRunning ?? this.isKartaAadharOtpTimerRunning,
      kartaAadharNumber: kartaAadharNumber ?? this.kartaAadharNumber,
      isKartaAadharVerifiedLoading: isKartaAadharVerifiedLoading ?? this.isKartaAadharVerifiedLoading,
      isKartaAadharVerified: isKartaAadharVerified ?? this.isKartaAadharVerified,
      kartaAadharVerificationFormKey: kartaAadharVerificationFormKey ?? this.kartaAadharVerificationFormKey,
      kartaFrontSideAdharFile: kartaFrontSideAdharFile ?? this.kartaFrontSideAdharFile,
      kartaBackSideAdharFile: kartaBackSideAdharFile ?? this.kartaBackSideAdharFile,
      isKartaAadharFileUploading: isKartaAadharFileUploading ?? this.isKartaAadharFileUploading,
      hufPanVerificationKey: hufPanVerificationKey ?? this.hufPanVerificationKey,
      hufPanNumberController: hufPanNumberController ?? this.hufPanNumberController,
      hufPanCardFile: hufPanCardFile ?? this.hufPanCardFile,
      isHUFPanVerifyingLoading: isHUFPanVerifyingLoading ?? this.isHUFPanVerifyingLoading,
      selectedUploadPanOption: selectedUploadPanOption ?? this.selectedUploadPanOption,
      businessPanNumberController: businessPanNumberController ?? this.businessPanNumberController,
      businessPanNameController: businessPanNameController ?? this.businessPanNameController,
      businessPanCardFile: businessPanCardFile ?? this.businessPanCardFile,
      isBusinessPanCardSaveLoading: isBusinessPanCardSaveLoading ?? this.isBusinessPanCardSaveLoading,
      isBusinessPanCardSave: isBusinessPanCardSave ?? this.isBusinessPanCardSave,
      businessPanVerificationKey: businessPanVerificationKey ?? this.businessPanVerificationKey,
      director1PanNameController: director1PanNameController ?? this.director1PanNameController,
      director1PanNumberController: director1PanNumberController ?? this.director1PanNumberController,
      director2BeneficialOwner: director2BeneficialOwner ?? this.director2BeneficialOwner,
      director2PanNameController: director2PanNameController ?? this.director2PanNameController,
      director2PanNumberController: director2PanNumberController ?? this.director2PanNumberController,
      director1PanCardFile: director1PanCardFile ?? this.director1PanCardFile,
      director2PanCardFile: director2PanCardFile ?? this.director2PanCardFile,
      directorsPanVerificationKey: directorsPanVerificationKey ?? this.directorsPanVerificationKey,
      ditector2BusinessRepresentative: ditector2BusinessRepresentative ?? this.ditector2BusinessRepresentative,
      director1BeneficialOwner: director1BeneficialOwner ?? this.director1BeneficialOwner,
      ditector1BusinessRepresentative: ditector1BusinessRepresentative ?? this.ditector1BusinessRepresentative,
      isDirectorPanCardSave: isDirectorPanCardSave ?? this.isDirectorPanCardSave,
      isDirectorPanCardSaveLoading: isDirectorPanCardSaveLoading ?? this.isDirectorPanCardSaveLoading,
      beneficialOwnerPanNameController: beneficialOwnerPanNameController ?? this.beneficialOwnerPanNameController,
      beneficialOwnerPanNumberController: beneficialOwnerPanNumberController ?? this.beneficialOwnerPanNumberController,
      beneficialOwnerPanCardFile: beneficialOwnerPanCardFile ?? this.beneficialOwnerPanCardFile,
      isBeneficialOwnerPanCardSave: isBeneficialOwnerPanCardSave ?? this.isBeneficialOwnerPanCardSave,
      isBeneficialOwnerPanCardSaveLoading:
          isBeneficialOwnerPanCardSaveLoading ?? this.isBeneficialOwnerPanCardSaveLoading,
      beneficialOwnerPanVerificationKey: beneficialOwnerPanVerificationKey ?? this.beneficialOwnerPanVerificationKey,
      beneficialOwnerIsDirector: beneficialOwnerIsDirector ?? this.beneficialOwnerIsDirector,
      benificialOwnerBusinessRepresentative:
          benificialOwnerBusinessRepresentative ?? this.benificialOwnerBusinessRepresentative,
      businessRepresentativeFormKey: businessRepresentativeFormKey ?? this.businessRepresentativeFormKey,
      businessRepresentativePanNameController:
          businessRepresentativePanNameController ?? this.businessRepresentativePanNameController,
      businessRepresentativePanNumberController:
          businessRepresentativePanNumberController ?? this.businessRepresentativePanNumberController,
      businessRepresentativeIsBenificalOwner:
          businessRepresentativeIsBenificalOwner ?? this.businessRepresentativeIsBenificalOwner,
      businessRepresentativeIsDirector: businessRepresentativeIsDirector ?? this.businessRepresentativeIsDirector,
      businessRepresentativePanCardFile: businessRepresentativePanCardFile ?? this.businessRepresentativePanCardFile,
      isbusinessRepresentativePanCardSave:
          isbusinessRepresentativePanCardSave ?? this.isbusinessRepresentativePanCardSave,
      isbusinessRepresentativePanCardSaveLoading:
          isbusinessRepresentativePanCardSaveLoading ?? this.isbusinessRepresentativePanCardSaveLoading,
      isPanDetailVerifyLoading: isPanDetailVerifyLoading ?? this.isPanDetailVerifyLoading,
      isPanDetailVerifySuccess: isPanDetailVerifySuccess ?? this.isPanDetailVerifySuccess,
      registerAddressFormKey: registerAddressFormKey ?? this.registerAddressFormKey,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      address1NameController: address1NameController ?? this.address1NameController,
      address2NameController: address2NameController ?? this.address2NameController,
      cityNameController: cityNameController ?? this.cityNameController,
      pinCodeController: pinCodeController ?? this.pinCodeController,
      stateNameController: stateNameController ?? this.stateNameController,
      addressVerificationFile: addressVerificationFile ?? this.addressVerificationFile,
      selectedAddressVerificationDocType: selectedAddressVerificationDocType ?? this.selectedAddressVerificationDocType,
      isAddressVerificationLoading: isAddressVerificationLoading ?? this.isAddressVerificationLoading,
      gstNumberController: gstNumberController ?? this.gstNumberController,
      turnOverController: turnOverController ?? this.turnOverController,
      gstCertificateFile: gstCertificateFile ?? this.gstCertificateFile,
      isGstVerificationLoading: isGstVerificationLoading ?? this.isGstVerificationLoading,
      annualTurnoverFormKey: annualTurnoverFormKey ?? this.annualTurnoverFormKey,
      isGstCertificateMandatory: isGstCertificateMandatory ?? this.isGstCertificateMandatory,
      iceNumberController: iceNumberController ?? this.iceNumberController,
      iceVerificationKey: iceVerificationKey ?? this.iceVerificationKey,
      isIceVerifyingLoading: isIceVerifyingLoading ?? this.isIceVerifyingLoading,
      iceCertificateFile: iceCertificateFile ?? this.iceCertificateFile,
      cinNumberController: cinNumberController ?? this.cinNumberController,
      cinVerificationKey: cinVerificationKey ?? this.cinVerificationKey,
      coiCertificateFile: coiCertificateFile ?? this.coiCertificateFile,
      isCINVerifyingLoading: isCINVerifyingLoading ?? this.isCINVerifyingLoading,
      llpinNumberController: llpinNumberController ?? this.llpinNumberController,
      uploadLLPAgreementFile: uploadLLPAgreementFile ?? this.uploadLLPAgreementFile,
      uploadPartnershipDeed: uploadPartnershipDeed ?? this.uploadPartnershipDeed,
      isBankAccountVerify: isBankAccountVerify ?? this.isBankAccountVerify,
      isBankAccountNumberVerifiedLoading: isBankAccountNumberVerifiedLoading ?? this.isBankAccountNumberVerifiedLoading,
      bankAccountNumberController: bankAccountNumberController ?? this.bankAccountNumberController,
      bankAccountVerificationFormKey: bankAccountVerificationFormKey ?? this.bankAccountVerificationFormKey,
      ifscCodeController: ifscCodeController ?? this.ifscCodeController,
      reEnterbankAccountNumberController: reEnterbankAccountNumberController ?? this.reEnterbankAccountNumberController,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      bankVerificationFile: bankVerificationFile ?? this.bankVerificationFile,
      selectedBankAccountVerificationDocType:
          selectedBankAccountVerificationDocType ?? this.selectedBankAccountVerificationDocType,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      ifscCode: ifscCode ?? this.ifscCode,
      isBankAccountVerificationLoading: isBankAccountVerificationLoading ?? this.isBankAccountVerificationLoading,
      selectedEstimatedMonthlyTransaction:
          selectedEstimatedMonthlyTransaction ?? this.selectedEstimatedMonthlyTransaction,
      curruncyList: curruncyList ?? this.curruncyList,
      selectedCurrencies: selectedCurrencies ?? this.selectedCurrencies,
      isTranscationDetailLoading: isTranscationDetailLoading ?? this.isTranscationDetailLoading,
      isBusinessOtpValidating: isBusinessOtpValidating ?? this.isBusinessOtpValidating,
      businessNatureString: businessNatureString ?? this.businessNatureString,
      estimatedMonthlyVolumeList: estimatedMonthlyVolumeList ?? this.estimatedMonthlyVolumeList,
      selectedAnnualTurnover: selectedAnnualTurnover ?? this.selectedAnnualTurnover,
      isCollapsed: isCollapsed ?? this.isCollapsed,
      isekycCollapsed: isekycCollapsed ?? this.isekycCollapsed,

      isDirectorCaptchaSend: isDirectorCaptchaSend ?? this.isDirectorCaptchaSend,
      isDirectorCaptchaLoading: isDirectorCaptchaLoading ?? this.isDirectorCaptchaLoading,
      directorCaptchaImage: directorCaptchaImage ?? this.directorCaptchaImage,
      directorCaptchaInputController: directorCaptchaInputController ?? this.directorCaptchaInputController,
      isDirectorAadharOtpLoading: isDirectorAadharOtpLoading ?? this.isDirectorAadharOtpLoading,
      isAadharOTPInvalidate: isAadharOTPInvalidate ?? this.isAadharOTPInvalidate,
      kartaCaptchaInputController: kartaCaptchaInputController ?? this.kartaCaptchaInputController,
      kartaCaptchaInput: kartaCaptchaInput ?? this.kartaCaptchaInput,
      isKartaCaptchaValid: isKartaCaptchaValid ?? this.isKartaCaptchaValid,
      isKartaCaptchaSubmitting: isKartaCaptchaSubmitting ?? this.isKartaCaptchaSubmitting,
      kartaCaptchaError: kartaCaptchaError ?? this.kartaCaptchaError,
      isKartaCaptchaSend: isKartaCaptchaSend ?? this.isKartaCaptchaSend,
      isKartaCaptchaLoading: isKartaCaptchaLoading ?? this.isKartaCaptchaLoading,
      kartaCaptchaImage: kartaCaptchaImage ?? this.kartaCaptchaImage,
      isKartaOtpLoading: isKartaOtpLoading ?? this.isKartaOtpLoading,
      kartaIsAadharOTPInvalidate: kartaIsAadharOTPInvalidate ?? this.kartaIsAadharOTPInvalidate,
      isSendOtpLoading: isSendOtpLoading ?? this.isSendOtpLoading,
      isVerifyBusinessRegisterdInfo: isVerifyBusinessRegisterdInfo ?? this.isVerifyBusinessRegisterdInfo,
      partnerAadharNumberController: partnerAadharNumberController ?? this.partnerAadharNumberController,
      partnerAadharOtpController: partnerAadharOtpController ?? this.partnerAadharOtpController,
      isPartnerOtpSent: isPartnerOtpSent ?? this.isPartnerOtpSent,
      partnerAadharOtpRemainingTime: partnerAadharOtpRemainingTime ?? this.partnerAadharOtpRemainingTime,
      isPartnerAadharOtpTimerRunning: isPartnerAadharOtpTimerRunning ?? this.isPartnerAadharOtpTimerRunning,
      partnerAadharNumber: partnerAadharNumber ?? this.partnerAadharNumber,
      isPartnerAadharVerifiedLoading: isPartnerAadharVerifiedLoading ?? this.isPartnerAadharVerifiedLoading,
      isPartnerAadharVerified: isPartnerAadharVerified ?? this.isPartnerAadharVerified,
      partnerAadharVerificationFormKey: partnerAadharVerificationFormKey ?? this.partnerAadharVerificationFormKey,
      partnerFrontSideAdharFile: partnerFrontSideAdharFile ?? this.partnerFrontSideAdharFile,
      partnerBackSideAdharFile: partnerBackSideAdharFile ?? this.partnerBackSideAdharFile,
      isPartnerAadharFileUploading: isPartnerAadharFileUploading ?? this.isPartnerAadharFileUploading,
      partnerCaptchaInputController: partnerCaptchaInputController ?? this.partnerCaptchaInputController,
      partnerCaptchaInput: partnerCaptchaInput ?? this.partnerCaptchaInput,
      isPartnerCaptchaValid: isPartnerCaptchaValid ?? this.isPartnerCaptchaValid,
      isPartnerCaptchaSubmitting: isPartnerCaptchaSubmitting ?? this.isPartnerCaptchaSubmitting,
      partnerCaptchaError: partnerCaptchaError ?? this.partnerCaptchaError,
      isPartnerCaptchaSend: isPartnerCaptchaSend ?? this.isPartnerCaptchaSend,
      isPartnerCaptchaLoading: isPartnerCaptchaLoading ?? this.isPartnerCaptchaLoading,
      partnerCaptchaImage: partnerCaptchaImage ?? this.partnerCaptchaImage,
      isPartnerOtpLoading: isPartnerOtpLoading ?? this.isPartnerOtpLoading,
      partnerIsAadharOTPInvalidate: partnerIsAadharOTPInvalidate ?? this.partnerIsAadharOTPInvalidate,
      proprietorAadharNumberController: proprietorAadharNumberController ?? this.proprietorAadharNumberController,
      proprietorAadharOtpController: proprietorAadharOtpController ?? this.proprietorAadharOtpController,
      isProprietorOtpSent: isProprietorOtpSent ?? this.isProprietorOtpSent,
      proprietorAadharOtpRemainingTime: proprietorAadharOtpRemainingTime ?? this.proprietorAadharOtpRemainingTime,
      isProprietorAadharOtpTimerRunning: isProprietorAadharOtpTimerRunning ?? this.isProprietorAadharOtpTimerRunning,
      proprietorAadharNumber: proprietorAadharNumber ?? this.proprietorAadharNumber,
      isProprietorAadharVerifiedLoading: isProprietorAadharVerifiedLoading ?? this.isProprietorAadharVerifiedLoading,
      isProprietorAadharVerified: isProprietorAadharVerified ?? this.isProprietorAadharVerified,
      proprietorAadharVerificationFormKey:
          proprietorAadharVerificationFormKey ?? this.proprietorAadharVerificationFormKey,
      proprietorFrontSideAdharFile: proprietorFrontSideAdharFile ?? this.proprietorFrontSideAdharFile,
      proprietorBackSideAdharFile: proprietorBackSideAdharFile ?? this.proprietorBackSideAdharFile,
      isProprietorAadharFileUploading: isProprietorAadharFileUploading ?? this.isProprietorAadharFileUploading,
      proprietorCaptchaInputController: proprietorCaptchaInputController ?? this.proprietorCaptchaInputController,
      proprietorCaptchaInput: proprietorCaptchaInput ?? this.proprietorCaptchaInput,
      isProprietorCaptchaValid: isProprietorCaptchaValid ?? this.isProprietorCaptchaValid,
      isProprietorCaptchaSubmitting: isProprietorCaptchaSubmitting ?? this.isProprietorCaptchaSubmitting,
      proprietorCaptchaError: proprietorCaptchaError ?? this.proprietorCaptchaError,
      isProprietorCaptchaSend: isProprietorCaptchaSend ?? this.isProprietorCaptchaSend,
      isProprietorCaptchaLoading: isProprietorCaptchaLoading ?? this.isProprietorCaptchaLoading,
      proprietorCaptchaImage: proprietorCaptchaImage ?? this.proprietorCaptchaImage,
      isProprietorOtpLoading: isProprietorOtpLoading ?? this.isProprietorOtpLoading,
      proprietorIsAadharOTPInvalidate: proprietorIsAadharOTPInvalidate ?? this.proprietorIsAadharOTPInvalidate,
      isLocalDataLoading: isLocalDataLoading ?? this.isLocalDataLoading,
      isCityAndStateLoading: isCityAndStateLoading ?? this.isCityAndStateLoading,
      isCityAndStateVerified: isCityAndStateVerified ?? this.isCityAndStateVerified,
      gstLegalName: gstLegalName ?? this.gstLegalName,
      isGSTNumberVerify: isGSTNumberVerify ?? this.isGSTNumberVerify,
      isHUFPanDetailsLoading: isHUFPanDetailsLoading ?? this.isHUFPanDetailsLoading,
      fullHUFNamePan: fullHUFNamePan ?? this.fullHUFNamePan,
      isHUFPanDetailsVerified: isHUFPanDetailsVerified ?? this.isHUFPanDetailsVerified,
      isDirector1PanDetailsLoading: isDirector1PanDetailsLoading ?? this.isDirector1PanDetailsLoading,
      fullDirector1NamePan: fullDirector1NamePan ?? this.fullDirector1NamePan,
      isDirector1PanDetailsVerified: isDirector1PanDetailsVerified ?? this.isDirector1PanDetailsVerified,
      fullDirector2NamePan: fullDirector2NamePan ?? this.fullDirector2NamePan,
      isDirector2PanDetailsLoading: isDirector2PanDetailsLoading ?? this.isDirector2PanDetailsLoading,
      isDirector2PanDetailsVerified: isDirector2PanDetailsVerified ?? this.isDirector2PanDetailsVerified,
      fullBeneficialOwnerNamePan: fullBeneficialOwnerNamePan ?? this.fullBeneficialOwnerNamePan,
      isBeneficialOwnerPanDetailsLoading: isBeneficialOwnerPanDetailsLoading ?? this.isBeneficialOwnerPanDetailsLoading,
      isBeneficialOwnerPanDetailsVerified:
          isBeneficialOwnerPanDetailsVerified ?? this.isBeneficialOwnerPanDetailsVerified,
      isBusinessRepresentativePanDetailsLoading:
          isBusinessRepresentativePanDetailsLoading ?? this.isBusinessRepresentativePanDetailsLoading,
      fullBusinessRepresentativeNamePan: fullBusinessRepresentativeNamePan ?? this.fullBusinessRepresentativeNamePan,
      isBusinessRepresentativePanDetailsVerified:
          isBusinessRepresentativePanDetailsVerified ?? this.isBusinessRepresentativePanDetailsVerified,
      directorEmailIdNumberController: directorEmailIdNumberController ?? this.directorEmailIdNumberController,
      directorMobileNumberController: directorMobileNumberController ?? this.directorMobileNumberController,
      directorContactInformationKey: directorContactInformationKey ?? this.directorContactInformationKey,
      isContactInfoSubmitLoading: isContactInfoSubmitLoading ?? this.isContactInfoSubmitLoading,
      isAuthorizedDirectorKycVerify: isAuthorizedDirectorKycVerify ?? this.isAuthorizedDirectorKycVerify,
      isOtherDirectorPanCardSave: isOtherDirectorPanCardSave ?? this.isOtherDirectorPanCardSave,
      isOtherDirectorKycVerify: isOtherDirectorKycVerify ?? this.isOtherDirectorKycVerify,
      isOtherDirectorPanCardSaveLoading: isOtherDirectorPanCardSaveLoading ?? this.isOtherDirectorPanCardSaveLoading,
      otherDirectorsPanVerificationKey: otherDirectorsPanVerificationKey ?? this.otherDirectorsPanVerificationKey,

      // Other Director Aadhar related properties
      isOtherAadharDirectorVerified: isOtherAadharDirectorVerified ?? this.isOtherAadharDirectorVerified,
      otherDirectorVerificationFormKey: otherDirectorVerificationFormKey ?? this.otherDirectorVerificationFormKey,
      otherDirectorAadharNumberController:
          otherDirectorAadharNumberController ?? this.otherDirectorAadharNumberController,
      otherDirectoraadharOtpController: otherDirectoraadharOtpController ?? this.otherDirectoraadharOtpController,
      isOtherDirectorOtpSent: isOtherDirectorOtpSent ?? this.isOtherDirectorOtpSent,
      isOtherDitectorOtpSent: isOtherDitectorOtpSent ?? this.isOtherDitectorOtpSent,
      otherDirectorAadharOtpRemainingTime:
          otherDirectorAadharOtpRemainingTime ?? this.otherDirectorAadharOtpRemainingTime,
      isOtherDirectorAadharOtpTimerRunning:
          isOtherDirectorAadharOtpTimerRunning ?? this.isOtherDirectorAadharOtpTimerRunning,
      otherDirectorAadharNumber: otherDirectorAadharNumber ?? this.otherDirectorAadharNumber,
      isOtherDirectorAadharVerifiedLoading:
          isOtherDirectorAadharVerifiedLoading ?? this.isOtherDirectorAadharVerifiedLoading,
      isOtherDirectorAadharVerified: isOtherDirectorAadharVerified ?? this.isOtherDirectorAadharVerified,
      otherDirectorAadharfrontSideAdharFile:
          otherDirectorAadharfrontSideAdharFile ?? this.otherDirectorAadharfrontSideAdharFile,
      otherDirectorAadharBackSideAdharFile:
          otherDirectorAadharBackSideAdharFile ?? this.otherDirectorAadharBackSideAdharFile,
      isOtherDirectorAadharFileUploading: isOtherDirectorAadharFileUploading ?? this.isOtherDirectorAadharFileUploading,

      // Other Director Captcha related properties
      isOtherDirectorCaptchaSend: isOtherDirectorCaptchaSend ?? this.isOtherDirectorCaptchaSend,
      isOtherDirectorDirectorCaptchaLoading:
          isOtherDirectorDirectorCaptchaLoading ?? this.isOtherDirectorDirectorCaptchaLoading,
      otherDirectorCaptchaImage: otherDirectorCaptchaImage ?? this.otherDirectorCaptchaImage,
      otherDirectorCaptchaInputController:
          otherDirectorCaptchaInputController ?? this.otherDirectorCaptchaInputController,
      isOtherDirectorAadharOtpLoading: isOtherDirectorAadharOtpLoading ?? this.isOtherDirectorAadharOtpLoading,
      isOtherAadharOTPInvalidate: isOtherAadharOTPInvalidate ?? this.isOtherAadharOTPInvalidate,
    );
  }
}
