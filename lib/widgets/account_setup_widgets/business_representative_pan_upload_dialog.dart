import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';

class BusinessRepresentativePanDetailDialog extends StatelessWidget {
  const BusinessRepresentativePanDetailDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            clipBehavior: Clip.hardEdge,
            constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                buildSizedBoxH(25.0),
                _buildDialogHeader(context),
                buildSizedBoxH(10.0),
                divider(context),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: ResponsiveHelper.getMaxDialogWidth(context),
                    maxHeight:
                        MediaQuery.of(context).size.height < 600
                            ? MediaQuery.of(context).size.height * 0.52
                            : MediaQuery.of(context).size.height * 0.7,
                  ),
                  child: SingleChildScrollView(
                    padding: ResponsiveHelper.getScreenPadding(context),
                    child: Form(
                      key: state.businessRepresentativeFormKey,
                      child: Center(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            buildSizedBoxH(20.0),
                            _buildRepresentativePanNumberField(context, state),
                            if (!state.isBusinessRepresentativePanDetailsVerified) buildSizedBoxH(24.0),
                            if (!state.isBusinessRepresentativePanDetailsVerified)
                              _buildVerifyPanButton(context, state),
                            buildSizedBoxH(24.0),
                            if (state.isBusinessRepresentativePanDetailsVerified == true) ...[
                              _buildBusinessRepresentativePanName(context, state),
                              buildSizedBoxH(24.0),
                              _buildBeneficialUploadPanCard(context, state),
                              buildSizedBoxH(44.0),
                              _buildBusinessPanSaveButton(),
                              buildSizedBoxH(ResponsiveHelper.isWebAndIsNotMobile(context) ? 60.0 : 20.0),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDialogHeader(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
        padding: ResponsiveHelper.getScreenPadding(context),
        child: Row(
          children: [
            Expanded(
              child: Text(
                Lang.of(context).lbl_business_representative_PAN_details,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: ResponsiveHelper.getFontSize(context, mobile: 20, tablet: 22, desktop: 24),
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.24,
                ),
              ),
            ),
            buildSizedboxW(15.0),
            CustomImageView(
              imagePath: Assets.images.svgs.icons.icClose.path,
              height: 50.0,
              onTap: () {
                GoRouter.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget divider(BuildContext context) =>
      Container(height: 1.5, width: double.maxFinite, color: Theme.of(context).customColors.lightBorderColor);

  Widget _buildRepresentativePanNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_PAN_number,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          controller: state.businessRepresentativePanNumberController,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          validator: (value) {
            return ExchekValidations.validatePANByType(value, "INDIVIDUAL");
          },
          maxLength: 10,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          inputFormatters: [UpperCaseTextFormatter()],
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(BusinessRepresentativePanNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            state.businessRepresentativeFormKey.currentState?.validate();
            if (ExchekValidations.validatePANByType(
                  state.businessRepresentativePanNumberController.text,
                  "INDIVIDUAL",
                ) ==
                null) {
              context.read<BusinessAccountSetupBloc>().add(
                GetBusinessRepresentativePanDetails(state.businessRepresentativePanNumberController.text),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildBeneficialUploadPanCard(BuildContext context, BusinessAccountSetupState state) {
    return CustomFileUploadWidget(
      title: "Upload Business Representative PAN Card",
      selectedFile: state.businessRepresentativePanCardFile,
      onFileSelected: (fileData) {
        context.read<BusinessAccountSetupBloc>().add(BusinessRepresentativeUploadPanCard(fileData));
      },
    );
  }

  // Widget _buildBeneficialOwnerIsDirector(BuildContext context, BusinessAccountSetupState state) {
  //   return CustomCheckBoxLabel(
  //     isSelected: state.businessRepresentativeIsBenificalOwner,
  //     label: Lang.of(context).lbl_person_beneficial_owner,
  //     onChanged: () {
  //       context.read<BusinessAccountSetupBloc>().add(
  //         ChangeBusinessReresentativeIsBeneficialOwner(isSelected: !state.businessRepresentativeIsBenificalOwner),
  //       );
  //     },
  //   );
  // }

  // Widget _buildBeneficialOwnerBusinessRepresentative(BuildContext context, BusinessAccountSetupState state) {
  //   return CustomCheckBoxLabel(
  //     isSelected: state.businessRepresentativeIsDirector,
  //     label: Lang.of(context).lbl_director_person,
  //     onChanged: () {
  //       context.read<BusinessAccountSetupBloc>().add(
  //         ChangeBusinessReresentativeOwnerIsDirector(isSelected: !state.businessRepresentativeIsDirector),
  //       );
  //     },
  //   );
  // }

  Widget _buildBusinessPanSaveButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isDisable =
            !(state.businessRepresentativePanCardFile != null &&
                state.businessRepresentativePanNumberController.text.isNotEmpty);
        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).lbl_save,
            borderRadius: 50.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 125 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isLoading: state.isbusinessRepresentativePanCardSaveLoading ?? false,
            isDisabled: isDisable,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.businessRepresentativeFormKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          SaveBusinessRepresentativePanDetails(
                            fileData: state.businessRepresentativePanCardFile,
                            panName: state.businessRepresentativePanNameController.text,
                            panNumber: state.businessRepresentativePanNumberController.text,
                          ),
                        );
                      }
                    },
          ),
        );
      },
    );
  }

  Widget _buildVerifyPanButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.businessRepresentativePanNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validatePANByType(state.businessRepresentativePanNumberController.text, "INDIVIDUAL") !=
              null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 125 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isBusinessRepresentativePanDetailsLoading,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 50.0,
            onPressed: () {
              // Show validation error if any
              state.businessRepresentativeFormKey.currentState?.validate();
              if (ExchekValidations.validatePANByType(
                    state.businessRepresentativePanNumberController.text,
                    "INDIVIDUAL",
                  ) ==
                  null) {
                context.read<BusinessAccountSetupBloc>().add(
                  GetBusinessRepresentativePanDetails(state.businessRepresentativePanNumberController.text),
                );
              }
            },
          );
        },
      ),
    );
  }

  Widget _buildBusinessRepresentativePanName(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_name_on_PAN,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(value: state.fullBusinessRepresentativeNamePan ?? '', showTrailingIcon: true),
      ],
    );
  }
}
