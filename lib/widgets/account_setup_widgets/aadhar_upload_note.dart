import 'package:exchek/core/utils/exports.dart';

class UploadNote extends StatelessWidget {
  final List<String> notes;
  final EdgeInsetsGeometry padding;
  final Color? backgroundColor;
  final double spacing;

  const UploadNote({
    super.key,
    required this.notes,
    this.padding = const EdgeInsets.all(22.0),
    this.backgroundColor,
    this.spacing = 14.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textStyle = theme.textTheme.bodyMedium?.copyWith(
      color: theme.customColors.blackColor,
      fontSize: ResponsiveHelper.getFontSize(context, mobile: 15.0, tablet: 16.0, desktop: 16.0),
      fontWeight: FontWeight.w400,
      letterSpacing: 0.16,
    );

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.customColors.lightBackgroundColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        children: List.generate(notes.length, (index) {
          return Padding(
            padding: EdgeInsets.only(bottom: index != notes.length - 1 ? spacing : 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("•", style: textStyle),
                buildSizedboxW(5.0),
                Expanded(child: Text(notes[index], style: textStyle)),
              ],
            ),
          );
        }),
      ),
    );
  }
}
