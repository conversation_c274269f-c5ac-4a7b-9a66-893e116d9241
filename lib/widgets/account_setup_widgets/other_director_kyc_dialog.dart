import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/account_setup_widgets/captcha_image.dart';
import 'package:exchek/widgets/common_widget/comman_verified_info_box.dart';

class OtherDirectorKycDialog extends StatelessWidget {
  const OtherDirectorKycDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            clipBehavior: Clip.hardEdge,
            constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                buildSizedBoxH(25.0),
                _buildDialogHeader(context),
                buildSizedBoxH(10.0),
                divider(context),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: ResponsiveHelper.getMaxDialogWidth(context),
                    maxHeight:
                        MediaQuery.of(context).size.height < 600
                            ? MediaQuery.of(context).size.height * 0.52
                            : MediaQuery.of(context).size.height * 0.7,
                  ),
                  child: SingleChildScrollView(
                    padding: ResponsiveHelper.getScreenPadding(context),
                    child:
                        (state.isOtherDirectorPanCardSave ?? false)
                            ? (state.isOtherDirectorAadharVerified == false
                                ? _buildIsNotAadharVerify(context, state)
                                : _buildIsAadharVerify(context, state))
                            : Form(
                              key: state.otherDirectorsPanVerificationKey,
                              child: Center(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,

                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    buildSizedBoxH(36.0),
                                    _buildPanDetailTitle(context),
                                    buildSizedBoxH(22.0),
                                    _buildDirector2PanNumberField(context, state),
                                    if (!state.isDirector2PanDetailsVerified) buildSizedBoxH(24.0),
                                    if (!state.isDirector2PanDetailsVerified)
                                      _buildVerifyDirector2PanButton(context, state),
                                    if (state.isDirector2PanDetailsVerified == true) ...[
                                      buildSizedBoxH(24.0),
                                      _buildDirector2PanName(context, state),
                                      buildSizedBoxH(24.0),
                                      _buildDirector2UploadPanCard(context, state),
                                      buildSizedBoxH(8.0),
                                      _buildDirector2IsBeneficialOwner(context, state),
                                      buildSizedBoxH(8.0),
                                      _buildDirector2BusinessRepresentative(context, state),
                                      buildSizedBoxH(20.0),
                                      _buildBusinessPanSaveButton(),
                                    ],
                                    buildSizedBoxH(36.0),
                                  ],
                                ),
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPanDetailTitle(BuildContext context) {
    return Text(
      "PAN Details",
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
        fontSize: ResponsiveHelper.getFontSize(context, mobile: 18, tablet: 20, desktop: 20),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildDirector2PanNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_PAN_number,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          controller: state.director2PanNumberController,
          textInputAction: TextInputAction.done,
          validator: (value) {
            return ExchekValidations.validatePANByType(value, "INDIVIDUAL");
          },
          maxLength: 10,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          inputFormatters: [UpperCaseTextFormatter()],
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(Director2PanNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            if (ExchekValidations.validatePANByType(state.director2PanNumberController.text, "INDIVIDUAL") == null) {
              context.read<BusinessAccountSetupBloc>().add(
                GetDirector2PanDetails(state.director2PanNumberController.text),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildDirector2UploadPanCard(BuildContext context, BusinessAccountSetupState state) {
    return CustomFileUploadWidget(
      title: "Upload Director 2 PAN Card",
      selectedFile: state.director2PanCardFile,
      onFileSelected: (fileData) {
        context.read<BusinessAccountSetupBloc>().add(Director2UploadPanCard(fileData));
      },
    );
  }

  Widget _buildDirector2BusinessRepresentative(BuildContext context, BusinessAccountSetupState state) {
    return CustomCheckBoxLabel(
      isSelected: state.ditector2BusinessRepresentative,
      label: Lang.of(context).lbl_business_Representative,
      onChanged: () {
        context.read<BusinessAccountSetupBloc>().add(
          ChangeDirector2IsBusinessRepresentative(isSelected: !state.ditector2BusinessRepresentative),
        );
      },
    );
  }

  Widget _buildDirector2IsBeneficialOwner(BuildContext context, BusinessAccountSetupState state) {
    return CustomCheckBoxLabel(
      isSelected: state.director2BeneficialOwner,
      label: Lang.of(context).lbl_this_person_beneficial_owner,
      onChanged: () {
        context.read<BusinessAccountSetupBloc>().add(
          ChangeDirector2IsBeneficialOwner(isSelected: !state.director2BeneficialOwner),
        );
      },
    );
  }

  Widget _buildVerifyDirector2PanButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.director2PanNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validatePANByType(state.director2PanNumberController.text, "INDIVIDUAL") != null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 130 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isDirector2PanDetailsLoading,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 18.0,
            onPressed: () {
              if (ExchekValidations.validatePANByType(state.director2PanNumberController.text, "INDIVIDUAL") == null) {
                context.read<BusinessAccountSetupBloc>().add(
                  GetDirector2PanDetails(state.director2PanNumberController.text),
                );
              }
            },
          );
        },
      ),
    );
  }

  Widget _buildDirector2PanName(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_name_on_PAN,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CommanVerifiedInfoBox(value: state.fullDirector2NamePan ?? '', showTrailingIcon: true),
      ],
    );
  }

  Widget _buildDialogHeader(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: ResponsiveHelper.getMaxDialogWidth(context)),
        padding: ResponsiveHelper.getScreenPadding(context),
        child: Row(
          children: [
            Expanded(
              child: Text(
                "Other Director KYC",
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: ResponsiveHelper.getFontSize(context, mobile: 20, tablet: 22, desktop: 24),
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.24,
                ),
              ),
            ),
            buildSizedboxW(15.0),
            CustomImageView(
              imagePath: Assets.images.svgs.icons.icClose.path,
              height: 50.0,
              onTap: () {
                GoRouter.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget divider(BuildContext context) =>
      Container(height: 1.5, width: double.maxFinite, color: Theme.of(context).customColors.lightBorderColor);

  Widget _buildBusinessPanSaveButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isDisable = !(state.director2PanCardFile != null && state.director2PanNumberController.text.isNotEmpty);
        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).save_and_next,
            borderRadius: 18.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 130 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isLoading: state.isOtherDirectorPanCardSaveLoading ?? false,
            isDisabled: isDisable,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.otherDirectorsPanVerificationKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          SaveOtherDirectorPanDetails(
                            director2fileData: state.director2PanCardFile,
                            directorpanName: state.director2PanNameController.text,
                            director2panNumber: state.director2PanNumberController.text,
                          ),
                        );
                      }
                    },
          ),
        );
      },
    );
  }

  Widget _buildContentTitle(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
        fontSize: ResponsiveHelper.getFontSize(context, mobile: 18, tablet: 20, desktop: 20),
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildIsAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.otherDirectorVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSizedBoxH(36.0),
          _buildContentTitle(context, "Aadhar Details"),
          buildSizedBoxH(22.0),
          _buildVerifyAadharNumber(context, state),
          // buildSizedBoxH(24.0),
          // _uolpadAadhaarTitle(context),
          // buildSizedBoxH(24.0),
          // UploadNote(notes: [Lang.of(context).lbl_note_1, Lang.of(context).lbl_note_2]),
          buildSizedBoxH(24.0),
          _buildUploadAddharCard(context),
          buildSizedBoxH(30.0),
          _buildNextButton(),
          buildSizedBoxH(36.0),
        ],
      ),
    );
  }

  Widget _buildIsNotAadharVerify(BuildContext context, BusinessAccountSetupState state) {
    return Form(
      key: state.otherDirectorVerificationFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildSizedBoxH(36.0),
          _buildContentTitle(context, "Aadhar Details"),
          buildSizedBoxH(22.0),
          _buildAadharNumberField(context, state),
          if (!state.isOtherDirectorCaptchaSend && !state.isOtherDirectorOtpSent) buildSizedBoxH(20),
          if (!state.isOtherDirectorCaptchaSend && !state.isOtherDitectorOtpSent)
            _buildVerifyAadharButton(context, state),
          if (state.isOtherDirectorCaptchaSend) ...[
            buildSizedBoxH(24.0),
            Builder(
              builder: (context) {
                if (state.otherDirectorCaptchaImage != null) {
                  return Column(
                    children: [
                      Row(
                        spacing: 20.0,
                        children: [
                          Base64CaptchaField(base64Image: state.otherDirectorCaptchaImage ?? ''),
                          AbsorbPointer(
                            absorbing: state.isOtherDirectorOtpSent,
                            child: Opacity(
                              opacity: state.isOtherDirectorOtpSent ? 0.5 : 1.0,
                              child: CustomImageView(
                                imagePath: Assets.images.svgs.icons.icRefresh.path,
                                height: 40.0,
                                width: 40.0,
                                onTap: () async {
                                  context.read<BusinessAccountSetupBloc>().add(OtherDirectorReCaptchaSend());
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      buildSizedBoxH(24.0),
                      _buildCaptchaField(context),
                      if (!state.isOtherDirectorOtpSent) ...[buildSizedBoxH(24.0), _buildSendOTPButton(context, state)],
                    ],
                  );
                } else {
                  return const SizedBox.shrink();
                }
              },
            ),
          ],
          if (state.isOtherDirectorOtpSent) ...[
            buildSizedBoxH(24.0),
            _buildOTPField(context, state),
            if (state.isOtherAadharOTPInvalidate.isNotEmpty) ...[
              buildSizedBoxH(10),
              Text(
                state.isOtherAadharOTPInvalidate,
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: const Color(0xffFF5D5F),
                ),
              ),
            ],
            buildSizedBoxH(30.0),
            _buildVerifyButton(context, state),
          ],
          buildSizedBoxH(36.0),
        ],
      ),
    );
  }

  Widget _buildAadharNumberField(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_aadhar_number,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
            height: 1.22,
          ),
        ),
        buildSizedBoxH(8.0),
        CustomTextInputField(
          context: context,
          type: InputType.digits,
          controller: state.otherDirectorAadharNumberController,
          maxLength: 14,
          textInputAction: TextInputAction.done,
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            GroupedInputFormatter(groupSizes: [4, 4, 4], separator: '-', digitsOnly: false, toUpperCase: true),
          ],
          validator: ExchekValidations.validateAadhaar,
          onChanged: (value) {
            context.read<BusinessAccountSetupBloc>().add(OtherDirectorAadharNumberChanged(value));
          },
          onFieldSubmitted: (value) {
            final isValidAadhar = state.otherDirectorAadharNumberController.text.trim().length == 14;
            if (isValidAadhar) {
              context.read<BusinessAccountSetupBloc>().add(OtherDirectorCaptchaSend());
            }
          },
        ),
      ],
    );
  }

  Widget _buildSendOTPButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.otherDirectorAadharNumberController]),
        builder: (context, child) {
          final isValidAadhar = state.otherDirectorAadharNumberController.text.trim().length == 14;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_send_otp,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isLoading: state.isOtherDirectorAadharOtpLoading ?? false,
            isDisabled: !isValidAadhar,
            borderRadius: 18.0,
            onPressed:
                isValidAadhar
                    ? () async {
                      final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                      context.read<BusinessAccountSetupBloc>().add(
                        OtherDirectorSendAadharOtp(
                          state.otherDirectorAadharNumberController.text.trim(),
                          state.otherDirectorCaptchaInputController.text.trim(),
                          sessionId,
                        ),
                      );
                    }
                    : null,
          );
        },
      ),
    );
  }

  Widget _buildOTPField(BuildContext context, BusinessAccountSetupState state) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Lang.of(context).lbl_OTP,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                fontWeight: FontWeight.w400,
                height: 1.22,
              ),
            ),
            buildSizedBoxH(8.0),
            CustomTextInputField(
              context: context,
              type: InputType.digits,
              controller: state.otherDirectoraadharOtpController,
              textInputAction: TextInputAction.done,
              validator: ExchekValidations.validateOTP,
              suffixText: true,
              suffixIcon: ValueListenableBuilder(
                valueListenable: state.otherDirectorAadharNumberController,
                builder: (context, _, __) {
                  return GestureDetector(
                    onTap:
                        state.isOtherDirectorAadharOtpTimerRunning
                            ? null
                            : () async {
                              FocusManager.instance.primaryFocus?.unfocus();
                              final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                              BlocProvider.of<BusinessAccountSetupBloc>(context).add(
                                OtherDirectorSendAadharOtp(
                                  state.otherDirectorAadharNumberController.text.trim(),
                                  state.otherDirectorCaptchaInputController.text.trim(),
                                  sessionId,
                                ),
                              );
                            },
                    child: Container(
                      color: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                      child: Text(
                        state.isOtherDirectorAadharOtpTimerRunning
                            ? '${Lang.of(context).lbl_resend_otp_in} ${formatSecondsToMMSS(state.otherDirectorAadharOtpRemainingTime)}sec'
                            : Lang.of(context).lbl_resend_OTP,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              state.isOtherDirectorAadharOtpTimerRunning ||
                                      (state.otherDirectorAadharNumberController.text.trim().length != 14)
                                  ? Theme.of(context).customColors.textdarkcolor?.withValues(alpha: 0.5)
                                  : Theme.of(context).customColors.greenColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
              maxLength: 6,
              contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
              inputFormatters: [FilteringTextInputFormatter.digitsOnly, NoPasteFormatter()],
              onFieldSubmitted: (value) {
                if (state.otherDirectorVerificationFormKey.currentState?.validate() ?? false) {
                  context.read<BusinessAccountSetupBloc>().add(
                    OtherDirectorAadharNumbeVerified(
                      state.otherDirectorAadharNumberController.text,
                      state.otherDirectoraadharOtpController.text,
                    ),
                  );
                }
              },
              contextMenuBuilder: (BuildContext context, EditableTextState editableTextState) {
                return AdaptiveTextSelectionToolbar.buttonItems(
                  anchors: editableTextState.contextMenuAnchors,
                  buttonItems:
                      editableTextState.contextMenuButtonItems
                          .where((item) => item.type != ContextMenuButtonType.paste)
                          .toList(),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          state.otherDirectoraadharOtpController,
          state.otherDirectorAadharNumberController,
        ]),
        builder: (context, child) {
          final isDisable =
              state.otherDirectoraadharOtpController.text.isEmpty ||
              state.otherDirectoraadharOtpController.text.isEmpty;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isOtherDirectorAadharVerifiedLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisable,
            borderRadius: 18.0,
            onPressed:
                isDisable
                    ? null
                    : () {
                      if (state.otherDirectorVerificationFormKey.currentState?.validate() ?? false) {
                        context.read<BusinessAccountSetupBloc>().add(
                          OtherDirectorAadharNumbeVerified(
                            state.otherDirectorAadharNumberController.text,
                            state.otherDirectoraadharOtpController.text,
                          ),
                        );
                      }
                    },
          );
        },
      ),
    );
  }

  Widget _buildVerifyAadharNumber(BuildContext context, BusinessAccountSetupState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_aadhar_number_verified,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
            fontWeight: FontWeight.w400,
          ),
        ),
        buildSizedBoxH(8.0),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 14.0, vertical: 14.0),
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.fillColor,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: Theme.of(context).customColors.greenColor!),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  ((state.otherDirectorAadharNumber ?? "").contains("-"))
                      ? (state.otherDirectorAadharNumber ?? '')
                      : GroupedInputFormatter.format(
                        input: state.otherDirectorAadharNumber ?? '',
                        groupSizes: [4, 4, 4],
                        separator: '-',
                        digitsOnly: true,
                      ),
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 14, desktop: 14),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              CustomImageView(imagePath: Assets.images.svgs.icons.icShieldTick.path, height: 20.0),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUploadAddharCard(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomFileUploadWidget(
              selectedFile: state.otherDirectorAadharfrontSideAdharFile,
              title: "Upload Front Side of Aadhar Card",
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(OtherDirectorFrontSlideAadharCardUpload(fileData!));
              },
            ),
            buildSizedBoxH(24.0),
            CustomFileUploadWidget(
              selectedFile: state.otherDirectorAadharBackSideAdharFile,
              title: "Upload Back Side of Aadhar Card",
              onFileSelected: (fileData) {
                context.read<BusinessAccountSetupBloc>().add(OtherDirectorBackSlideAadharCardUpload(fileData!));
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildNextButton() {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        final isButtonEnabled =
            state.otherDirectorAadharfrontSideAdharFile != null && state.otherDirectorAadharBackSideAdharFile != null;

        return Align(
          alignment: Alignment.centerRight,
          child: CustomElevatedButton(
            isShowTooltip: true,
            text: Lang.of(context).save_and_next,
            borderRadius: 18.0,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            buttonTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 16.0,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            isDisabled: !isButtonEnabled,
            isLoading: state.isOtherDirectorAadharFileUploading,
            onPressed:
                isButtonEnabled
                    ? () {
                      context.read<BusinessAccountSetupBloc>().add(
                        OtherDirectorAadharFileUploadSubmitted(
                          frontAadharFileData: state.otherDirectorAadharfrontSideAdharFile!,
                          backAadharFileData: state.otherDirectorAadharBackSideAdharFile!,
                        ),
                      );
                    }
                    : null,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
          ),
        );
      },
    );
  }

  String formatSecondsToMMSS(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$secs';
  }

  Widget _buildCaptchaField(BuildContext context) {
    return BlocBuilder<BusinessAccountSetupBloc, BusinessAccountSetupState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Enter Captcha",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: ResponsiveHelper.getFontSize(context, mobile: 14, tablet: 15, desktop: 16),
                    fontWeight: FontWeight.w400,
                    height: 1.22,
                  ),
                ),
                buildSizedBoxH(8.0),
                AbsorbPointer(
                  absorbing: state.isOtherDirectorOtpSent && state.otherDirectorCaptchaInputController.text.isNotEmpty,
                  child: CustomTextInputField(
                    context: context,
                    type: InputType.text,
                    controller: state.otherDirectorCaptchaInputController,
                    textInputAction: TextInputAction.done,
                    contentPadding: EdgeInsets.symmetric(vertical: 12.0, horizontal: 14.0),
                    onFieldSubmitted:
                        state.isOtherDirectorDirectorCaptchaLoading == true
                            ? null
                            : (value) async {
                              final isCaptchaValid = state.otherDirectorCaptchaInputController.text.isNotEmpty;

                              if (isCaptchaValid) {
                                final sessionId = await Prefobj.preferences.get(Prefkeys.sessionId) ?? '';
                                context.read<BusinessAccountSetupBloc>().add(
                                  OtherDirectorSendAadharOtp(
                                    state.otherDirectorAadharNumberController.text.trim(),
                                    state.otherDirectorCaptchaInputController.text.trim(),
                                    sessionId,
                                  ),
                                );
                              }
                            },
                    validator: ExchekValidations.validateRequired,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildVerifyAadharButton(BuildContext context, BusinessAccountSetupState state) {
    return Align(
      alignment: Alignment.centerRight,
      child: AnimatedBuilder(
        animation: Listenable.merge([state.otherDirectorAadharNumberController]),
        builder: (context, _) {
          bool isDisabled =
              ExchekValidations.validateAadhaar(
                state.otherDirectorAadharNumberController.text.replaceAll("-", "").trim(),
              ) !=
              null;
          return CustomElevatedButton(
            text: Lang.of(context).lbl_verify,
            width: ResponsiveHelper.isWebAndIsNotMobile(context) ? 150 : double.maxFinite,
            isShowTooltip: true,
            isLoading: state.isOtherDirectorDirectorCaptchaLoading ?? false,
            tooltipMessage: Lang.of(context).lbl_tooltip_text,
            isDisabled: isDisabled,
            borderRadius: 18.0,
            onPressed:
                isDisabled
                    ? null
                    : () {
                      final isValidAadhar =
                          ExchekValidations.validateAadhaar(
                            state.otherDirectorAadharNumberController.text.replaceAll("-", "").trim(),
                          ) ==
                          null;
                      if (isValidAadhar) {
                        context.read<BusinessAccountSetupBloc>().add(OtherDirectorCaptchaSend());
                      }
                    },
          );
        },
      ),
    );
  }
}
