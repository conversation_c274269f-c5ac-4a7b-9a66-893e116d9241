import 'package:dotted_border/dotted_border.dart';
import 'package:exchek/core/utils/exports.dart';
import 'package:exchek/widgets/common_widget/app_loader_widget.dart';
import 'package:exchek/widgets/common_widget/app_toast_message.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'package:pdfx/pdfx.dart';

class FileData {
  final String name;
  final Uint8List bytes;
  final String? path;
  final double sizeInMB;
  final String? webPath;

  FileData({required this.name, required this.bytes, this.path, required this.sizeInMB, this.webPath});
}

class CustomFileUploadWidget extends StatefulWidget {
  final String title;
  final List<String> allowedExtensions;
  final double maxSizeInMB;
  final bool allowZipFiles;
  final Function(FileData?)? onFileSelected;
  final FileData? selectedFile;

  const CustomFileUploadWidget({
    super.key,
    this.title = '',
    this.allowedExtensions = const ['jpg', 'jpeg', 'png', 'pdf'],
    this.maxSizeInMB = 2.0,
    this.allowZipFiles = false,
    this.onFileSelected,
    this.selectedFile,
  });

  @override
  State<CustomFileUploadWidget> createState() => _CustomFileUploadWidgetState();
}

class _CustomFileUploadWidgetState extends State<CustomFileUploadWidget> {
  FileData? selectedFile;
  bool isUploading = false;
  DropzoneViewController? _dropzoneController;
  bool _isDropHovered = false;

  @override
  void initState() {
    super.initState();
    selectedFile = widget.selectedFile;
  }

  Future<void> _pickFile() async {
    try {
      setState(() {
        isUploading = true;
      });

      if (kIsWeb) {
        await _pickFromFiles();
      } else {
        await _showFilePickerOptions();
      }
    } catch (e) {
      AppToast.show(message: 'Error picking file: $e', type: ToastificationType.error);
    } finally {
      setState(() {
        isUploading = false;
      });
    }
  }

  Future<void> _showFilePickerOptions() async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.fillColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              buildSizedBoxH(20),
              Text(
                'Select File Source',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              buildSizedBoxH(20),
              _buildOptionTile(
                icon: Icons.photo_library,
                title: 'Gallery',
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery();
                },
              ),
              _buildOptionTile(
                icon: Icons.folder,
                title: 'Files',
                onTap: () {
                  Navigator.pop(context);
                  _pickFromFiles();
                },
              ),
              buildSizedBoxH(10.0),
            ],
          ),
        );
      },
    );
  }

  Widget _buildOptionTile({required IconData icon, required String title, required VoidCallback onTap}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(icon, color: Theme.of(context).primaryColor),
              buildSizedBoxH(15),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const Spacer(),
              Icon(Icons.arrow_forward_ios, size: 16, color: Theme.of(context).customColors.textdarkcolor),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        Uint8List bytes = await image.readAsBytes();
        String? path = image.path;
        String? webPath;

        if (kIsWeb) {
          webPath = 'web_${DateTime.now().millisecondsSinceEpoch}_${image.name}';
        }

        FileData fileData = FileData(
          name: image.name,
          bytes: bytes,
          path: path,
          webPath: webPath,
          sizeInMB: bytes.length / (1024 * 1024),
        );
        await _validateAndSetFile(fileData);
      }
    } catch (e) {
      AppToast.show(message: 'Error accessing gallery: $e', type: ToastificationType.error);
    }
  }

  Future<void> _pickFromFiles() async {
    try {
      // Build allowed extensions list based on widget settings
      List<String> allowedExtensions = List.from(widget.allowedExtensions);
      if (widget.allowZipFiles && !allowedExtensions.contains('zip')) {
        allowedExtensions.add('zip');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        PlatformFile platformFile = result.files.first;

        // Validate extension immediately
        String extension = platformFile.name.split('.').last.toLowerCase();
        if (!widget.allowedExtensions.contains(extension)) {
          AppToast.show(
            message: 'Invalid file type. Only ${widget.allowedExtensions.join(', ')} allowed',
            type: ToastificationType.warning,
          );
          return;
        }

        Uint8List? fileBytes = platformFile.bytes;
        if (fileBytes == null) {
          if (!kIsWeb && platformFile.path != null) {
            fileBytes = await File(platformFile.path!).readAsBytes();
          } else {
            AppToast.show(message: 'Unable to read file data', type: ToastificationType.error);
            return;
          }
        }

        FileData fileData = FileData(
          name: platformFile.name,
          bytes: fileBytes,
          path: platformFile.path,
          sizeInMB: platformFile.size / (1024 * 1024),
        );

        await _validateAndSetFile(fileData);
      }
    } catch (e) {
      AppToast.show(message: 'Error picking file: $e', type: ToastificationType.error);
    }
  }

  Future<void> _validateAndSetFile(FileData fileData) async {
    try {
      // Get file extension in lowercase
      String extension = fileData.name.split('.').last.toLowerCase();

      // Define allowed MIME types and extensions
      final allowedExtensions = {'pdf', 'jpg', 'jpeg', 'png'};

      // Add ZIP to allowed extensions if enabled
      if (widget.allowZipFiles) {
        allowedExtensions.add('zip');
      }

      // Validate file extension
      if (!allowedExtensions.contains(extension)) {
        String allowedFormats = widget.allowZipFiles ? 'PDF, JPG, PNG, ZIP' : 'PDF, JPG, PNG';
        AppToast.show(message: 'Invalid file format. Only $allowedFormats allowed', type: ToastificationType.warning);
        return;
      }

      // Validate file size
      if (fileData.sizeInMB > widget.maxSizeInMB) {
        AppToast.show(
          message: 'File size (${fileData.sizeInMB.toStringAsFixed(1)}MB) exceeds ${widget.maxSizeInMB}MB limit',
          type: ToastificationType.warning,
        );
        return;
      }

      setState(() {
        selectedFile = fileData;
      });

      if (widget.onFileSelected != null) {
        widget.onFileSelected!(fileData);
      }
    } catch (e) {
      AppToast.show(message: 'Error validating file: $e', type: ToastificationType.error);
    }
  }

  void _showFilePreview() {
    if (selectedFile == null) return;

    String extension = selectedFile!.name.split('.').last.toLowerCase();
    bool isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
    bool isPdf = extension == 'pdf';

    if (isImage) {
      _showImagePreview();
    } else if (isPdf) {
      _openPdfViewer();
    } else {
      AppToast.show(message: 'Preview not available for this file type', type: ToastificationType.error);
    }
  }

  void _showImagePreview() {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            constraints: BoxConstraints(
              // maxWidth: MediaQuery.of(context).size.width * 0.9,
              maxWidth: ResponsiveHelper.getMaxDialogWidth(context),
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).customColors.fillColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor!,
                    borderRadius: const BorderRadius.only(topLeft: Radius.circular(20), topRight: Radius.circular(20)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.image, color: Theme.of(context).primaryColor, size: 24),
                      buildSizedboxW(12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Image Preview',
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.grey[800]),
                            ),
                            Text(
                              selectedFile!.name,
                              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(Icons.close, color: Colors.grey[600]),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[200],
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                    ],
                  ),
                ),
                // Image
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.memory(
                        selectedFile!.bytes,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 150,
                            decoration: BoxDecoration(color: Colors.grey[100], borderRadius: BorderRadius.circular(12)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                                buildSizedBoxH(8),
                                Text('Unable to load image', style: TextStyle(color: Colors.grey[600])),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
                // Footer
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
                      buildSizedboxW(8),
                      Text(
                        'Size: ${selectedFile!.sizeInMB.toStringAsFixed(2)}MB',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper to clone Uint8List for web to avoid detached ArrayBuffer errors
  Uint8List _cloneUint8List(Uint8List data) {
    return Uint8List.fromList(data);
  }

  Future<void> _openPdfViewer() async {
    if (selectedFile == null) {
      AppToast.show(message: 'Invalid or empty file selected', type: ToastificationType.warning);
      return;
    }

    try {
      final controller = PdfController(
        document: PdfDocument.openData(kIsWeb ? _cloneUint8List(selectedFile!.bytes) : selectedFile!.bytes),
      );

      if (!mounted) return;

      await showDialog(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.black.withValues(alpha: 0.8),
        builder:
            (_) => Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: const EdgeInsets.all(20),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: ResponsiveHelper.getMaxDialogWidth(context),
                  maxHeight: MediaQuery.of(context).size.height * 0.85,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.fillColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.picture_as_pdf, color: Theme.of(context).primaryColor),
                          buildSizedboxW(12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'PDF Preview',
                                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.grey[800]),
                                ),
                                Text(
                                  selectedFile!.name,
                                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: Icon(Icons.close, color: Colors.grey[600]),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.grey[200],
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // PDF View
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: PdfView(controller: controller, scrollDirection: Axis.vertical, pageSnapping: false),
                      ),
                    ),

                    // Footer
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(20),
                          bottomRight: Radius.circular(20),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
                          buildSizedboxW(8),
                          Text(
                            'Size: ${selectedFile!.sizeInMB.toStringAsFixed(2)}MB',
                            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
      );

      // Dispose the controller after dialog is closed
      controller.dispose();
    } catch (e) {
      AppToast.show(message: 'Failed to open PDF: $e', type: ToastificationType.error);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title.isNotEmpty) ...[
          Text(
            widget.title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: ResponsiveHelper.getFontSize(context, mobile: 15.0, tablet: 16.0, desktop: 16.0),
              fontWeight: FontWeight.w400,
              height: 1.22,
            ),
          ),
          buildSizedBoxH(8.0),
        ],
        DottedBorder(
          options: RoundedRectDottedBorderOptions(
            radius: Radius.circular(8.0),
            borderPadding: EdgeInsets.zero,
            color: Theme.of(context).customColors.borderColor!,
            dashPattern: [5, 5],
            strokeWidth: 1.5,
          ),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.0),
              color: Theme.of(context).customColors.fillColor,
            ),
            child: selectedFile != null ? _buildSelectedFileWidget() : _buildUploadWidget(),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadWidget() {
    return SizedBox(
      height: 150.0,
      child:
          kIsWeb
              ? Stack(
                children: [
                  DropzoneView(
                    onCreated: (controller) => _dropzoneController = controller,
                    operation: DragOperation.copy,
                    onHover: () => setState(() => _isDropHovered = true),
                    onLeave: () => setState(() => _isDropHovered = false),
                    onDropFile: (file) async {
                      setState(() => isUploading = true);
                      try {
                        final name = await _dropzoneController!.getFilename(file);
                        final bytes = await _dropzoneController!.getFileData(file);
                        final size = bytes.length / (1024 * 1024);
                        final webPath = 'web_${DateTime.now().millisecondsSinceEpoch}_$name';
                        final fileData = FileData(
                          name: name,
                          bytes: bytes,
                          path: webPath,
                          webPath: webPath,
                          sizeInMB: size,
                        );
                        await _validateAndSetFile(fileData);
                      } catch (e) {
                        AppToast.show(message: 'Error dropping file: $e', type: ToastificationType.error);
                      } finally {
                        setState(() => isUploading = false);
                      }
                    },
                  ),
                  GestureDetector(
                    onTap: _pickFile,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: double.infinity,
                      height: 150.0,
                      decoration: BoxDecoration(
                        color:
                            _isDropHovered
                                ? Theme.of(context).customColors.primaryColor?.withValues(alpha: 0.12)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Center(
                        child:
                            isUploading
                                ? const AppLoaderWidget()
                                : Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      height: 44.0,
                                      width: 44.0,
                                      decoration: BoxDecoration(shape: BoxShape.circle, color: Color(0xFFF5F5F5)),
                                      alignment: Alignment.center,
                                      child: CustomImageView(
                                        imagePath: Assets.images.svgs.icons.icDocumentUpload.path,
                                        height: 24.0,
                                        width: 24.0,
                                        color: Theme.of(context).customColors.blueColor,
                                      ),
                                    ),
                                    buildSizedBoxH(8.0),
                                    Text.rich(
                                      TextSpan(
                                        text: "Click to Upload ",
                                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          color: Theme.of(context).customColors.blueColor,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: "or drag and drop",
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w400,
                                              color: Theme.of(context).customColors.blackColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    buildSizedBoxH(7.0),
                                    Text(
                                      'Max ${widget.maxSizeInMB}MB in ${_getAllowedFormatsText()} format',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        color: Theme.of(context).customColors.textdarkcolor,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                      ),
                    ),
                  ),
                ],
              )
              : GestureDetector(
                onTap: _pickFile,
                child: Container(
                  width: double.infinity,
                  height: 150.0,
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(color: Theme.of(context).customColors.borderColor!, width: 2),
                  ),
                  child: Center(
                    child:
                        isUploading
                            ? const AppLoaderWidget()
                            : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  height: 44.0,
                                  width: 44.0,
                                  decoration: BoxDecoration(shape: BoxShape.circle, color: Color(0xFFF5F5F5)),
                                  alignment: Alignment.center,
                                  child: CustomImageView(
                                    imagePath: Assets.images.svgs.icons.icDocumentUpload.path,
                                    height: 24.0,
                                    width: 24.0,
                                    color: Theme.of(context).customColors.blueColor,
                                  ),
                                ),
                                buildSizedBoxH(8.0),
                                Text.rich(
                                  TextSpan(
                                    text: "Click to Upload ",
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Theme.of(context).customColors.blueColor,
                                    ),
                                    children: [
                                      TextSpan(
                                        text: "or drag and drop",
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: Theme.of(context).customColors.blackColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                buildSizedBoxH(7.0),
                                Text(
                                  'Max ${widget.maxSizeInMB}MB in ${_getAllowedFormatsText()} format',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Theme.of(context).customColors.textdarkcolor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                  ),
                ),
              ),
    );
  }

  Widget _buildSelectedFileWidget() {
    String fileExtension = _getFileExtension(selectedFile!);
    bool isImage = ['JPG', 'JPEG', 'PNG', 'GIF', 'BMP', 'WEBP'].contains(fileExtension);
    bool isPdf = fileExtension == 'PDF';
    bool isZip = fileExtension == 'ZIP';
    bool canPreview = isImage || isPdf || isZip;
    bool isRemoteFile = selectedFile!.path != null && selectedFile!.path!.startsWith('http');

    Widget filePreviewWidget;
    if (isImage) {
      if (selectedFile!.bytes.isNotEmpty) {
        // Local image preview
        filePreviewWidget = ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.memory(
            selectedFile!.bytes,
            width: double.infinity,
            height: 150,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Center(child: Icon(Icons.image_not_supported_outlined, size: 32, color: Colors.grey[400]));
            },
          ),
        );
      } else if (isRemoteFile) {
        // Remote image preview
        filePreviewWidget = ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            selectedFile!.path!,
            width: double.infinity,
            height: 150,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Center(child: Icon(Icons.image_not_supported_outlined, size: 32, color: Colors.grey[400]));
            },
          ),
        );
      } else {
        filePreviewWidget = Center(child: Icon(Icons.image_not_supported_outlined, size: 32, color: Colors.grey[400]));
      }
    } else if (isPdf && isRemoteFile) {
      // Remote PDF preview: show a button to open in browser
      // filePreviewWidget = Center(
      //   child: ElevatedButton.icon(
      //     onPressed: () {
      //       launchUrlString(selectedFile!.path!);
      //     },
      //     icon: Icon(Icons.picture_as_pdf, color: Theme.of(context).customColors.redColor),
      //     label: Text('View PDF'),
      //     style: ElevatedButton.styleFrom(
      //       backgroundColor: Theme.of(context).customColors.fillColor,
      //       foregroundColor: Theme.of(context).customColors.redColor,
      //     ),
      //   ),
      // );
      filePreviewWidget = Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.picture_as_pdf, size: 32, color: Theme.of(context).customColors.redColor),
            buildSizedBoxH(8),
            Text(
              fileExtension,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).customColors.redColor,
              ),
            ),
          ],
        ),
      );
    } else {
      // Default: show icon
      filePreviewWidget = Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getFileIcon(fileExtension),
              size: 32,
              color:
                  isPdf
                      ? Theme.of(context).customColors.redColor
                      : isZip
                      ? Theme.of(context).customColors.purpleColor
                      : Theme.of(context).primaryColor,
            ),
            buildSizedBoxH(8),
            Text(
              fileExtension,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color:
                    isPdf
                        ? Theme.of(context).customColors.redColor
                        : isZip
                        ? Theme.of(context).customColors.purpleColor
                        : Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 150,
          decoration: BoxDecoration(
            color: Theme.of(context).customColors.fillColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Stack(
            children: [
              // File content or icon
              filePreviewWidget,

              // Action buttons overlay
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).customColors.shadowColor!.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (canPreview)
                        IconButton(
                          onPressed: _pickFile,
                          icon: CustomImageView(
                            imagePath: Assets.images.svgs.icons.icUploadFile.path,
                            color: Theme.of(context).customColors.primaryColor,
                            height: 18.0,
                            width: 18.0,
                            onTap: _pickFile,
                          ),
                          tooltip: 'Upload',
                        ),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 8.0),
                        height: 24.0,
                        width: 1.2,
                        color: Theme.of(context).customColors.primaryColor?.withValues(alpha: 0.2),
                      ),
                      IconButton(
                        onPressed: _showFilePreview,
                        icon: CustomImageView(
                          imagePath: Assets.images.svgs.icons.icView.path,
                          height: 18.0,
                          width: 18.0,
                          color: Theme.of(context).customColors.primaryColor,
                          onTap: _showFilePreview,
                        ),
                        style: IconButton.styleFrom(
                          foregroundColor: Theme.of(context).primaryColor,
                          padding: const EdgeInsets.all(8),
                          minimumSize: const Size(32, 32),
                        ),
                        tooltip: isPdf ? 'Open PDF' : 'Preview',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // File info
        // Container(
        //   padding: const EdgeInsets.all(16),
        //   decoration: BoxDecoration(
        //     color: Theme.of(context).customColors.fillColor,
        //     borderRadius: BorderRadius.circular(12),
        //     border: Border.all(color: Theme.of(context).customColors.borderColor!),
        //   ),
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       Text(
        //         fileName,
        //         style: Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 14, fontWeight: FontWeight.w600),
        //         maxLines: 2,
        //         overflow: TextOverflow.ellipsis,
        //       ),
        //       buildSizedBoxH(8),
        //       Row(
        //         children: [
        //           Container(
        //             padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        //             decoration: BoxDecoration(
        //               color: Theme.of(context).customColors.greenColor?.withValues(alpha: 0.1),
        //               borderRadius: BorderRadius.circular(6),
        //             ),
        //             child: Row(
        //               mainAxisSize: MainAxisSize.min,
        //               children: [
        //                 Icon(Icons.check_circle, size: 12, color: Colors.green[600]),
        //                 buildSizedboxW(4),
        //                 Text(
        //                   'Uploaded',
        //                   style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        //                     color: Theme.of(context).customColors.greenColor,
        //                     fontWeight: FontWeight.w500,
        //                     fontSize: 11,
        //                   ),
        //                 ),
        //               ],
        //             ),
        //           ),
        //           buildSizedboxW(12),
        //           Text(
        //             '${selectedFile!.sizeInMB.toStringAsFixed(1)}MB',
        //             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        //               color: Theme.of(context).customColors.textdarkcolor,
        //               fontWeight: FontWeight.w400,
        //               fontSize: 12,
        //             ),
        //           ),
        //           if (canPreview) ...[
        //             const Spacer(),
        //             GestureDetector(
        //               onTap: _showFilePreview,
        //               child: Container(
        //                 padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        //                 decoration: BoxDecoration(
        //                   color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        //                   borderRadius: BorderRadius.circular(6),
        //                 ),
        //                 child: Row(
        //                   mainAxisSize: MainAxisSize.min,
        //                   children: [
        //                     Icon(
        //                       isPdf ? Icons.open_in_new : Icons.visibility,
        //                       size: 12,
        //                       color: Theme.of(context).primaryColor,
        //                     ),
        //                     buildSizedboxW(4),
        //                     Text(
        //                       isPdf ? 'Open' : 'View',
        //                       style: Theme.of(context).textTheme.bodySmall?.copyWith(
        //                         color: Theme.of(context).primaryColor,
        //                         fontSize: 11,
        //                         fontWeight: FontWeight.w500,
        //                       ),
        //                     ),
        //                   ],
        //                 ),
        //               ),
        //             ),
        //           ],
        //         ],
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'zip':
        return Icons.folder_zip;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return Icons.image;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _getFileExtension(FileData fileData) {
    // Prefer extension from path if it's a URL, else from name
    if (fileData.path != null && fileData.path!.contains('.')) {
      final uri = Uri.tryParse(fileData.path!);
      if (uri != null && uri.pathSegments.isNotEmpty) {
        final last = uri.pathSegments.last;
        if (last.contains('.')) {
          return last.split('.').last.split('?').first.toUpperCase();
        }
      }
      // fallback: extract from path string
      final pathPart = fileData.path!.split('/').last;
      if (pathPart.contains('.')) {
        return pathPart.split('.').last.split('?').first.toUpperCase();
      }
    }
    // fallback: extract from name
    if (fileData.name.contains('.')) {
      return fileData.name.split('.').last.toUpperCase();
    }
    return fileData.name.toUpperCase();
  }

  String _getAllowedFormatsText() {
    List<String> formats = List.from(widget.allowedExtensions);
    if (widget.allowZipFiles && !formats.contains('zip')) {
      formats.add('zip');
    }
    return formats.map((e) => e.toUpperCase()).join('/');
  }

  // ignore: unused_element
  void _removeFile() {
    setState(() {
      selectedFile = null;
    });
    if (widget.onFileSelected != null) {
      widget.onFileSelected!(null);
    }
  }
}
